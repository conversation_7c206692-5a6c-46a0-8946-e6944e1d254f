version: '3.8'

services:
  livekit-server:
    image: livekit/livekit-server:latest
    ports:
      - "7880:7880"
      - "7881:7881"
      - "50000-50100:50000-50100/udp"
    environment:
      - LIVEKIT_KEYS=APIUQUwG76Wo6Xd:wqVmuTKidmOdyAD9bbJAoWpucQrMfDEB6MZIiydiIfq
      - LIVEKIT_RTC_USE_EXTERNAL_IP=true
    restart: unless-stopped
    networks:
      - livekit-network

  livekit-service:
    build: .
    ports:
      - "3002:3002"
    environment:
      - LIVEKIT_URL=http://livekit-server:7880
      - LIVEKIT_WS_URL=ws://livekit-server:7880
      - API_KEY=APIUQUwG76Wo6Xd
      - API_SECRET=wqVmuTKidmOdyAD9bbJAoWpucQrMfDEB6MZIiydiIfq
      - TWITCH_STREAM_KEY=live_1048713352_TbNczEfLtlt6wRdvxXLBrfqBBQ72Ax
    depends_on:
      - livekit-server
    restart: unless-stopped
    networks:
      - livekit-network

networks:
  livekit-network:
    driver: bridge
