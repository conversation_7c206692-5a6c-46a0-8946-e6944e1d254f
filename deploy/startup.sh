#!/bin/bash

# LiveKit Cloud Deployment Script
echo "🚀 Starting LiveKit deployment on GCE..."

# Update system
sudo apt-get update -y
sudo apt-get install -y curl

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

echo "🐳 Docker installed successfully"

# Create LiveKit deployment directory
mkdir -p ~/livekit-deploy
cd ~/livekit-deploy

# Create docker-compose.yml
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  livekit-server:
    image: livekit/livekit-server:latest
    ports:
      - "7880:7880"
      - "7881:7881"
      - "50000-50100:50000-50100/udp"
    environment:
      - LIVEKIT_KEYS=APIUQUwG76Wo6Xd:wqVmuTKidmOdyAD9bbJAoWpucQrMfDEB6MZIiydiIfq
      - LIVEKIT_RTC_USE_EXTERNAL_IP=true
    restart: unless-stopped
    networks:
      - livekit-network

  livekit-service:
    build: .
    ports:
      - "3002:3002"
    environment:
      - LIVEKIT_URL=http://livekit-server:7880
      - LIVEKIT_WS_URL=ws://livekit-server:7880
      - API_KEY=APIUQUwG76Wo6Xd
      - API_SECRET=wqVmuTKidmOdyAD9bbJAoWpucQrMfDEB6MZIiydiIfq
      - TWITCH_STREAM_KEY=live_1048713352_TbNczEfLtlt6wRdvxXLBrfqBBQ72Ax
    depends_on:
      - livekit-server
    restart: unless-stopped
    networks:
      - livekit-network

networks:
  livekit-network:
    driver: bridge
EOF

# Create Dockerfile
cat > Dockerfile << 'EOF'
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy source code
COPY . ./

# Expose port
EXPOSE 3002

# Start the service
CMD ["node", "server.js"]
EOF

# Create package.json
cat > package.json << 'EOF'
{
  "name": "livekit-rtmp-service",
  "version": "1.0.0",
  "description": "LiveKit service for WebRTC to RTMP streaming",
  "main": "server.js",
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "livekit-server-sdk": "^2.6.1"
  }
}
EOF

# Create server.js
cat > server.js << 'EOF'
const express = require('express');
const cors = require('cors');
const { AccessToken, EgressClient } = require('livekit-server-sdk');

const app = express();
const port = 3002;

app.use(cors());
app.use(express.json());

const LIVEKIT_URL = process.env.LIVEKIT_URL || 'http://livekit-server:7880';
const LIVEKIT_WS_URL = process.env.LIVEKIT_WS_URL || 'ws://livekit-server:7880';
const API_KEY = process.env.API_KEY || 'APIUQUwG76Wo6Xd';
const API_SECRET = process.env.API_SECRET || 'wqVmuTKidmOdyAD9bbJAoWpucQrMfDEB6MZIiydiIfq';
const TWITCH_STREAM_KEY = process.env.TWITCH_STREAM_KEY || 'live_1048713352_TbNczEfLtlt6wRdvxXLBrfqBBQ72Ax';

const egressClient = new EgressClient(LIVEKIT_URL, API_KEY, API_SECRET);

app.post('/token', async (req, res) => {
  try {
    const { roomName, participantName } = req.body;
    
    const token = new AccessToken(API_KEY, API_SECRET, {
      identity: participantName,
      ttl: '1h',
    });

    token.addGrant({
      room: roomName,
      roomJoin: true,
      canPublish: true,
      canSubscribe: true,
    });

    const jwt = await token.toJwt();
    
    res.json({ 
      token: jwt,
      url: LIVEKIT_WS_URL,
      roomName,
      participantName
    });
  } catch (error) {
    console.error('Error generating token:', error);
    res.status(500).json({ error: 'Failed to generate token' });
  }
});

app.post('/start-stream', async (req, res) => {
  try {
    const { roomName } = req.body;
    
    const egressRequest = {
      roomName: roomName,
      rtmp: {
        urls: [`rtmp://live.twitch.tv/app/${TWITCH_STREAM_KEY}`]
      },
      video: {
        codec: 'H264_BASELINE',
        width: 1920,
        height: 1080,
        framerate: 30,
        bitrate: 2500000,
      },
      audio: {
        codec: 'AAC',
        bitrate: 128000,
        frequency: 48000,
        channels: 2,
      }
    };

    const egress = await egressClient.startRoomCompositeEgress(egressRequest);
    
    res.json({ 
      success: true, 
      egressId: egress.egressId,
      status: egress.status,
      message: 'RTMP stream to Twitch started successfully'
    });

  } catch (error) {
    console.error('Error starting RTMP egress:', error);
    res.status(500).json({ 
      error: 'Failed to start RTMP stream',
      details: error.message 
    });
  }
});

app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    livekit_url: LIVEKIT_URL,
    timestamp: new Date().toISOString()
  });
});

app.listen(port, '0.0.0.0', () => {
  console.log(`🚀 LiveKit RTMP service running on http://0.0.0.0:${port}`);
});
EOF

echo "📁 Deployment files created"

# Start the services
echo "🚀 Starting LiveKit services..."
sudo docker-compose up -d

echo "✅ LiveKit deployed successfully!"
echo "📡 LiveKit Server: http://$(curl -s ifconfig.me):7880"
echo "🔧 LiveKit Service: http://$(curl -s ifconfig.me):3002"
echo ""
echo "🎉 Deployment complete! Update your client to use the external IP above."
EOF

chmod +x startup.sh
