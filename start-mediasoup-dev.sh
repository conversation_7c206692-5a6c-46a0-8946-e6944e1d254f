#!/bin/bash

# Start development environment for WebRTC-to-RTMP streaming with mediasoup

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting Mediasoup WebRTC-to-RTMP Development Environment${NC}"
echo "=============================================================="
echo ""

# Function to check if port is in use
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Check if mediasoup service is running
echo "🔍 Checking mediasoup service..."
if curl -s http://localhost:3000/health > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Mediasoup service is already running${NC}"
else
    echo -e "${YELLOW}⚠️  Mediasoup service not running on port 3000${NC}"
    echo ""
    echo -e "${BLUE}Starting mediasoup service...${NC}"
    
    # Check if mediasoup-service directory exists
    if [ ! -d "mediasoup-service" ]; then
        echo -e "${RED}❌ mediasoup-service directory not found${NC}"
        echo "Please make sure you have the mediasoup-service directory"
        exit 1
    fi
    
    cd mediasoup-service
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        echo "📦 Installing mediasoup dependencies..."
        npm install
    fi
    
    # Create logs directory
    mkdir -p logs
    
    # Start mediasoup service in background
    echo "🎬 Starting mediasoup service..."
    npm start &
    MEDIASOUP_PID=$!
    cd ..
    
    # Wait for service to be ready
    echo "⏳ Waiting for mediasoup service to be ready..."
    for i in {1..30}; do
        if curl -s http://localhost:3000/health > /dev/null 2>&1; then
            echo -e "${GREEN}✅ Mediasoup service is ready${NC}"
            break
        fi
        sleep 1
        if [ $i -eq 30 ]; then
            echo -e "${RED}❌ Mediasoup service failed to start${NC}"
            exit 1
        fi
    done
fi

echo ""

# Check frontend dependencies
echo "📦 Checking frontend dependencies..."
if [ ! -d "node_modules" ]; then
    echo "   Installing frontend dependencies..."
    npm install
fi

echo ""
echo -e "${BLUE}🎉 Development environment is ready!${NC}"
echo "===================================="
echo ""
echo -e "${GREEN}Services:${NC}"
echo "🎬 Mediasoup Service: http://localhost:3000"
echo "🔧 Health Check:      http://localhost:3000/health"
echo "📊 Sessions API:      http://localhost:3000/sessions"
echo ""
echo -e "${GREEN}Configuration:${NC}"
echo "Frontend will connect to mediasoup service at http://localhost:3000"
echo ""

# Cleanup function
cleanup() {
    echo ""
    echo -e "${YELLOW}🛑 Stopping services...${NC}"
    if [ ! -z "$MEDIASOUP_PID" ]; then
        kill $MEDIASOUP_PID 2>/dev/null || true
        echo "Stopped mediasoup service"
    fi
    exit
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Start the frontend
echo -e "${BLUE}🎨 Starting React frontend...${NC}"
echo "Frontend will be available at: http://localhost:5173"
echo ""
echo -e "${YELLOW}Press Ctrl+C to stop all services${NC}"
echo ""

npm run dev

# Wait for frontend to exit
wait
