{"name": "mediasoup-webrtc-rtmp-service", "version": "1.0.0", "description": "WebRTC to RTMP streaming service using mediasoup", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "docker:build": "docker build -t mediasoup-service .", "docker:run": "docker run -p 3000:3000 -p 40000-49999:40000-49999/udp mediasoup-service"}, "dependencies": {"mediasoup": "^3.14.9", "socket.io": "^4.7.5", "express": "^4.18.2", "uuid": "^9.0.1", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}, "license": "ISC", "keywords": ["webrtc", "rtmp", "streaming", "mediasoup", "twitch"]}