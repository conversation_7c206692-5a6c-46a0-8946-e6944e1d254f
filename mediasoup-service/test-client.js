#!/usr/bin/env node

const io = require('socket.io-client');

console.log('🧪 Testing Mediasoup WebRTC-to-RTMP Service...\n');

// Configuration
const SERVICE_URL = process.env.SERVICE_URL || 'http://localhost:3000';
const RTMP_URL = process.env.RTMP_URL || 'rtmp://live.twitch.tv/app';
const STREAM_KEY = process.env.STREAM_KEY || 'your-stream-key-here';

if (STREAM_KEY === 'your-stream-key-here') {
  console.log('❌ Please set STREAM_KEY environment variable');
  console.log('Usage: STREAM_KEY=your_key node test-client.js');
  process.exit(1);
}

// Connect to service
console.log(`🔗 Connecting to ${SERVICE_URL}...`);
const socket = io(SERVICE_URL, {
  transports: ['websocket', 'polling']
});

let sessionId = null;

socket.on('connect', () => {
  console.log('✅ Connected to mediasoup service');
  console.log(`🆔 Socket ID: ${socket.id}`);
  
  // Start streaming session
  console.log('🚀 Starting stream session...');
  socket.emit('start-stream', {
    rtmpUrl: RTMP_URL,
    streamKey: STREAM_KEY,
    roomId: 'test-room'
  });
});

socket.on('stream-session-created', ({ sessionId: id, success, error }) => {
  if (success) {
    sessionId = id;
    console.log(`✅ Stream session created: ${sessionId}`);
    
    // Simulate WebRTC offer (this would normally come from browser)
    console.log('📤 Sending mock WebRTC offer...');
    
    const mockOffer = {
      dtlsParameters: {
        role: 'client',
        fingerprints: [
          {
            algorithm: 'sha-256',
            value: 'mock:fingerprint:for:testing'
          }
        ]
      }
    };
    
    socket.emit('webrtc-offer', {
      sessionId: sessionId,
      offer: mockOffer
    });
    
  } else {
    console.error(`❌ Failed to create stream session: ${error}`);
    process.exit(1);
  }
});

socket.on('webrtc-answer', ({ sessionId, answer }) => {
  console.log('📥 Received WebRTC answer');
  console.log(`🎬 Session ${sessionId} should now be ready for streaming`);
  
  // In a real implementation, you would:
  // 1. Set the answer as remote description
  // 2. Start sending video/audio tracks
  // 3. Handle ICE candidates
  
  console.log('💡 Note: This is a mock test. Real video streaming requires browser WebRTC APIs.');
  
  // Keep running for a bit to test the session
  setTimeout(() => {
    console.log('🛑 Stopping test session...');
    socket.emit('stop-stream', { sessionId });
  }, 10000);
});

socket.on('stream-stopped', ({ sessionId, success, error }) => {
  if (success) {
    console.log('✅ Stream stopped successfully');
  } else {
    console.error(`❌ Error stopping stream: ${error}`);
  }
  process.exit(0);
});

socket.on('error', (error) => {
  console.error('❌ Socket error:', error);
  process.exit(1);
});

socket.on('connect_error', (error) => {
  console.error('❌ Connection error:', error);
  process.exit(1);
});

socket.on('disconnect', (reason) => {
  console.log(`🔌 Disconnected: ${reason}`);
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Test interrupted');
  if (sessionId) {
    socket.emit('stop-stream', { sessionId });
  }
  socket.disconnect();
  process.exit(0);
});

// Test service health
console.log('🏥 Testing service health...');
const http = require('http');

const healthCheck = http.get(`${SERVICE_URL}/health`, (res) => {
  let data = '';
  res.on('data', chunk => data += chunk);
  res.on('end', () => {
    try {
      const health = JSON.parse(data);
      console.log('✅ Service health check passed');
      console.log(`📊 Workers: ${health.workers}, Sessions: ${health.sessions}`);
    } catch (error) {
      console.error('❌ Invalid health response:', data);
    }
  });
});

healthCheck.on('error', (error) => {
  console.error('❌ Health check failed:', error.message);
  console.error('💡 Make sure the service is running on', SERVICE_URL);
  process.exit(1);
});
