<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mediasoup Camera Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        
        .container {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        video {
            width: 100%;
            max-width: 640px;
            height: auto;
            background: #000;
            border-radius: 5px;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        .status {
            background: #333;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 14px;
        }
        
        .error {
            background: #d32f2f;
            color: white;
        }
        
        .success {
            background: #388e3c;
            color: white;
        }
        
        .info {
            background: #1976d2;
            color: white;
        }
        
        input {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #555;
            border-radius: 3px;
            background: #333;
            color: white;
        }
        
        label {
            display: block;
            margin-top: 10px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>🎥 Mediasoup Camera Test</h1>
    
    <div class="container">
        <h2>RTMP Configuration</h2>
        <label for="rtmpUrl">RTMP URL:</label>
        <input type="text" id="rtmpUrl" value="rtmp://live.twitch.tv/app" placeholder="rtmp://live.twitch.tv/app">
        
        <label for="streamKey">Stream Key:</label>
        <input type="text" id="streamKey" value="live_1048713352_TbNczEfLtlt6wRdvxXLBrfqBBQ72Ax" placeholder="Your Twitch stream key">
    </div>
    
    <div class="container">
        <h2>Camera Preview</h2>
        <video id="localVideo" autoplay muted playsinline></video>
        <br>
        <button id="startCamera">📹 Start Camera</button>
        <button id="stopCamera" disabled>⏹️ Stop Camera</button>
    </div>
    
    <div class="container">
        <h2>Streaming</h2>
        <button id="startStream" disabled>🚀 Start Streaming to Twitch</button>
        <button id="stopStream" disabled>⏹️ Stop Streaming</button>
    </div>
    
    <div class="container">
        <h2>Status</h2>
        <div id="status" class="status">Ready to start...</div>
    </div>

    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <script type="module">
        import * as mediasoupClient from 'https://jspm.dev/mediasoup-client@3';

        class MediasoupCameraTest {
            constructor() {
                this.socket = null;
                this.pc = null;
                this.localStream = null;
                this.sessionId = null;
                
                this.setupEventListeners();
            }
            
            setupEventListeners() {
                document.getElementById('startCamera').onclick = () => this.startCamera();
                document.getElementById('stopCamera').onclick = () => this.stopCamera();
                document.getElementById('startStream').onclick = () => this.startStreaming();
                document.getElementById('stopStream').onclick = () => this.stopStreaming();
            }
            
            log(message, type = 'info') {
                const status = document.getElementById('status');
                const timestamp = new Date().toLocaleTimeString();
                status.innerHTML = `[${timestamp}] ${message}`;
                status.className = `status ${type}`;
                console.log(`[${timestamp}] ${message}`);
            }
            
            async startCamera() {
                try {
                    this.log('🎥 Starting camera...', 'info');
                    
                    this.localStream = await navigator.mediaDevices.getUserMedia({
                        video: {
                            width: { ideal: 1280 },
                            height: { ideal: 720 },
                            frameRate: { ideal: 30 }
                        },
                        audio: true
                    });
                    
                    const video = document.getElementById('localVideo');
                    video.srcObject = this.localStream;
                    
                    document.getElementById('startCamera').disabled = true;
                    document.getElementById('stopCamera').disabled = false;
                    document.getElementById('startStream').disabled = false;
                    
                    this.log('✅ Camera started successfully', 'success');
                    
                } catch (error) {
                    this.log(`❌ Camera error: ${error.message}`, 'error');
                }
            }
            
            stopCamera() {
                if (this.localStream) {
                    this.localStream.getTracks().forEach(track => track.stop());
                    this.localStream = null;
                    
                    const video = document.getElementById('localVideo');
                    video.srcObject = null;
                }
                
                document.getElementById('startCamera').disabled = false;
                document.getElementById('stopCamera').disabled = true;
                document.getElementById('startStream').disabled = true;
                
                this.log('⏹️ Camera stopped', 'info');
            }
            
            async startStreaming() {
                try {
                    const rtmpUrl = document.getElementById('rtmpUrl').value;
                    const streamKey = document.getElementById('streamKey').value;
                    
                    if (!rtmpUrl || !streamKey) {
                        this.log('❌ Please enter RTMP URL and stream key', 'error');
                        return;
                    }
                    
                    if (!this.localStream) {
                        this.log('❌ Please start camera first', 'error');
                        return;
                    }
                    
                    this.log('🔌 Connecting to mediasoup service...', 'info');
                    
                    // Connect to mediasoup service
                    this.socket = io('http://localhost:3000');
                    
                    this.socket.on('connect', () => {
                        this.log('✅ Connected to mediasoup service', 'success');
                        this.createStreamSession(rtmpUrl, streamKey);
                    });
                    
                    this.socket.on('connect_error', (error) => {
                        this.log(`❌ Connection failed: ${error.message}`, 'error');
                    });
                    
                } catch (error) {
                    this.log(`❌ Streaming error: ${error.message}`, 'error');
                }
            }
            
            createStreamSession(rtmpUrl, streamKey) {
                this.log('📡 Creating stream session...', 'info');
                
                // Listen for session creation
                this.socket.on('stream-session-created', async ({ sessionId, success, error }) => {
                    if (success) {
                        this.sessionId = sessionId;
                        this.log(`✅ Session created: ${sessionId}`, 'success');
                        await this.setupWebRTC();
                    } else {
                        this.log(`❌ Session creation failed: ${error}`, 'error');
                    }
                });
                
                // Create the session
                this.socket.emit('start-stream', {
                    rtmpUrl,
                    streamKey,
                    roomId: 'camera-test'
                });
            }
            
            async setupWebRTC() {
                try {
                    this.log('🔗 Setting up mediasoup-client...', 'info');

                    this.log('✅ mediasoup-client ES module loaded', 'success');

                    // Create mediasoup device
                    this.device = new mediasoupClient.Device();

                    // Get router capabilities from server
                    this.log('📡 Getting router capabilities...', 'info');
                    this.socket.emit('get-router-capabilities', { sessionId: this.sessionId });

                    // Wait for router capabilities
                    this.socket.once('router-capabilities', async ({ sessionId, routerRtpCapabilities }) => {
                        if (sessionId === this.sessionId) {
                            try {
                                // Load device with router capabilities
                                await this.device.load({ routerRtpCapabilities });
                                this.log('✅ Device loaded with router capabilities', 'success');

                                // Create send transport
                                this.log('🚛 Creating send transport...', 'info');
                                this.socket.emit('create-send-transport', { sessionId: this.sessionId });

                            } catch (error) {
                                this.log(`❌ Failed to load device: ${error.message}`, 'error');
                            }
                        }
                    });

                    // Handle send transport creation
                    this.socket.once('send-transport-created', async ({ sessionId, transportOptions }) => {
                        if (sessionId === this.sessionId) {
                            try {
                                this.log('🚛 Send transport created, connecting...', 'info');

                                // Create send transport
                                this.sendTransport = this.device.createSendTransport(transportOptions);

                                // Handle transport connection
                                this.sendTransport.on('connect', async ({ dtlsParameters }, callback, errback) => {
                                    try {
                                        this.socket.emit('connect-send-transport', {
                                            sessionId: this.sessionId,
                                            dtlsParameters
                                        });
                                        callback();
                                    } catch (error) {
                                        errback(error);
                                    }
                                });

                                // Handle producer creation
                                this.sendTransport.on('produce', async ({ kind, rtpParameters }, callback, errback) => {
                                    try {
                                        this.socket.emit('produce', {
                                            sessionId: this.sessionId,
                                            kind,
                                            rtpParameters
                                        });

                                        this.socket.once('produced', ({ producerId }) => {
                                            callback({ id: producerId });
                                        });
                                    } catch (error) {
                                        errback(error);
                                    }
                                });

                                // Produce video track
                                const videoTrack = this.localStream.getVideoTracks()[0];

                                // Debug video track
                                this.log(`🔍 Video track info: enabled=${videoTrack.enabled}, readyState=${videoTrack.readyState}, muted=${videoTrack.muted}`, 'info');
                                this.log(`🔍 Video track settings: ${JSON.stringify(videoTrack.getSettings())}`, 'info');

                                this.videoProducer = await this.sendTransport.produce({ track: videoTrack });

                                // Debug producer
                                this.log(`🔍 Producer info: id=${this.videoProducer.id}, closed=${this.videoProducer.closed}, paused=${this.videoProducer.paused}`, 'info');

                                // Monitor producer stats
                                setInterval(async () => {
                                    try {
                                        const stats = await this.videoProducer.getStats();
                                        const outboundStats = Array.from(stats.values()).find(s => s.type === 'outbound-rtp');
                                        if (outboundStats) {
                                            this.log(`📊 Producer stats: packets=${outboundStats.packetsSent}, bytes=${outboundStats.bytesSent}`, 'info');
                                        }
                                    } catch (error) {
                                        console.error('Stats error:', error);
                                    }
                                }, 2000);

                                this.log('✅ Video producer created!', 'success');
                                this.log('🚀 Camera stream should now be live on Twitch!', 'success');

                                document.getElementById('startStream').disabled = true;
                                document.getElementById('stopStream').disabled = false;

                            } catch (error) {
                                this.log(`❌ Failed to create send transport: ${error.message}`, 'error');
                            }
                        }
                    });

                } catch (error) {
                    this.log(`❌ mediasoup-client setup failed: ${error.message}`, 'error');
                }
            }
            
            extractDtlsParameters(sdp) {
                const lines = sdp.split('\n');
                const fingerprintLine = lines.find(line => line.trim().startsWith('a=fingerprint:'));

                if (!fingerprintLine) {
                    throw new Error('No fingerprint found in SDP');
                }

                const fingerprintContent = fingerprintLine.replace('a=fingerprint:', '').trim();
                const [algorithm, value] = fingerprintContent.split(' ');

                return {
                    role: 'client',
                    fingerprints: [{
                        algorithm: algorithm.toLowerCase(),
                        value: value.toUpperCase()
                    }]
                };
            }

            createMinimalAnswerSDP(answer) {
                const { iceParameters, dtlsParameters } = answer;

                // Create a very minimal SDP answer that matches our offer structure
                let sdp = `v=0\r\n`;
                sdp += `o=- 0 2 IN IP4 127.0.0.1\r\n`;
                sdp += `s=-\r\n`;
                sdp += `t=0 0\r\n`;
                sdp += `a=group:BUNDLE 0 1\r\n`;
                sdp += `a=msid-semantic: WMS\r\n`;

                // Audio section (minimal)
                sdp += `m=audio 9 UDP/TLS/RTP/SAVPF 111\r\n`;
                sdp += `c=IN IP4 0.0.0.0\r\n`;
                sdp += `a=rtcp:9 IN IP4 0.0.0.0\r\n`;
                sdp += `a=ice-ufrag:${iceParameters.usernameFragment}\r\n`;
                sdp += `a=ice-pwd:${iceParameters.password}\r\n`;
                sdp += `a=ice-options:trickle\r\n`;
                sdp += `a=fingerprint:${dtlsParameters.fingerprints[0].algorithm} ${dtlsParameters.fingerprints[0].value}\r\n`;
                sdp += `a=setup:active\r\n`;
                sdp += `a=mid:0\r\n`;
                sdp += `a=sendrecv\r\n`;
                sdp += `a=rtcp-mux\r\n`;
                sdp += `a=rtpmap:111 opus/48000/2\r\n`;

                // Video section (minimal)
                sdp += `m=video 9 UDP/TLS/RTP/SAVPF 96\r\n`;
                sdp += `c=IN IP4 0.0.0.0\r\n`;
                sdp += `a=rtcp:9 IN IP4 0.0.0.0\r\n`;
                sdp += `a=ice-ufrag:${iceParameters.usernameFragment}\r\n`;
                sdp += `a=ice-pwd:${iceParameters.password}\r\n`;
                sdp += `a=ice-options:trickle\r\n`;
                sdp += `a=fingerprint:${dtlsParameters.fingerprints[0].algorithm} ${dtlsParameters.fingerprints[0].value}\r\n`;
                sdp += `a=setup:active\r\n`;
                sdp += `a=mid:1\r\n`;
                sdp += `a=sendrecv\r\n`;
                sdp += `a=rtcp-mux\r\n`;
                sdp += `a=rtpmap:96 VP8/90000\r\n`;

                return sdp;
            }

            createAnswerSDP(answer) {
                const { iceParameters, iceCandidates, dtlsParameters } = answer;

                let sdp = `v=0\r\n`;
                sdp += `o=- 0 2 IN IP4 127.0.0.1\r\n`;
                sdp += `s=-\r\n`;
                sdp += `t=0 0\r\n`;
                sdp += `a=group:BUNDLE 0 1\r\n`;
                sdp += `a=msid-semantic: WMS\r\n`;

                // Audio media section (first, to match offer order)
                sdp += `m=audio 9 UDP/TLS/RTP/SAVPF 111\r\n`;
                sdp += `c=IN IP4 0.0.0.0\r\n`;
                sdp += `a=rtcp:9 IN IP4 0.0.0.0\r\n`;

                // ICE parameters for audio
                if (iceParameters) {
                    sdp += `a=ice-ufrag:${iceParameters.usernameFragment}\r\n`;
                    sdp += `a=ice-pwd:${iceParameters.password}\r\n`;
                }
                sdp += `a=ice-options:trickle\r\n`;

                // DTLS parameters for audio
                if (dtlsParameters && dtlsParameters.fingerprints && dtlsParameters.fingerprints.length > 0) {
                    const fp = dtlsParameters.fingerprints[0];
                    sdp += `a=fingerprint:${fp.algorithm} ${fp.value}\r\n`;
                }
                sdp += `a=setup:active\r\n`;

                // Audio media attributes
                sdp += `a=mid:0\r\n`;
                sdp += `a=sendrecv\r\n`;
                sdp += `a=rtcp-mux\r\n`;
                sdp += `a=rtpmap:111 opus/48000/2\r\n`;

                // ICE candidates for audio
                if (iceCandidates && iceCandidates.length > 0) {
                    iceCandidates.forEach(candidate => {
                        const foundation = candidate.foundation || '1';
                        const component = candidate.component || 1;
                        const protocol = candidate.protocol || 'udp';
                        const priority = candidate.priority || 2113667326;
                        const ip = candidate.ip || '127.0.0.1';
                        const port = candidate.port || 9;
                        const type = candidate.type || 'host';

                        sdp += `a=candidate:${foundation} ${component} ${protocol} ${priority} ${ip} ${port} typ ${type}\r\n`;
                    });
                }

                // Video media section (second, to match offer order)
                sdp += `m=video 9 UDP/TLS/RTP/SAVPF 96\r\n`;
                sdp += `c=IN IP4 0.0.0.0\r\n`;
                sdp += `a=rtcp:9 IN IP4 0.0.0.0\r\n`;

                // ICE parameters for video (same as audio)
                if (iceParameters) {
                    sdp += `a=ice-ufrag:${iceParameters.usernameFragment}\r\n`;
                    sdp += `a=ice-pwd:${iceParameters.password}\r\n`;
                }
                sdp += `a=ice-options:trickle\r\n`;

                // DTLS parameters for video (same as audio)
                if (dtlsParameters && dtlsParameters.fingerprints && dtlsParameters.fingerprints.length > 0) {
                    const fp = dtlsParameters.fingerprints[0];
                    sdp += `a=fingerprint:${fp.algorithm} ${fp.value}\r\n`;
                }
                sdp += `a=setup:active\r\n`;

                // Video media attributes
                sdp += `a=mid:1\r\n`;
                sdp += `a=sendrecv\r\n`;
                sdp += `a=rtcp-mux\r\n`;
                sdp += `a=rtpmap:96 VP8/90000\r\n`;

                // ICE candidates for video (same as audio)
                if (iceCandidates && iceCandidates.length > 0) {
                    iceCandidates.forEach(candidate => {
                        const foundation = candidate.foundation || '1';
                        const component = candidate.component || 1;
                        const protocol = candidate.protocol || 'udp';
                        const priority = candidate.priority || 2113667326;
                        const ip = candidate.ip || '127.0.0.1';
                        const port = candidate.port || 9;
                        const type = candidate.type || 'host';

                        sdp += `a=candidate:${foundation} ${component} ${protocol} ${priority} ${ip} ${port} typ ${type}\r\n`;
                    });
                }

                return sdp;
            }
            


            stopStreaming() {
                if (this.socket) {
                    if (this.sessionId) {
                        this.socket.emit('stop-stream', { sessionId: this.sessionId });
                    }
                    this.socket.disconnect();
                    this.socket = null;
                }

                if (this.pc) {
                    this.pc.close();
                    this.pc = null;
                }

                this.sessionId = null;

                document.getElementById('startStream').disabled = false;
                document.getElementById('stopStream').disabled = true;

                this.log('⏹️ Streaming stopped', 'info');
            }
        }
        
        // Initialize the test
        const test = new MediasoupCameraTest();
    </script>
</body>
</html>
