<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mediasoup Camera Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        
        .container {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        video {
            width: 100%;
            max-width: 640px;
            height: auto;
            background: #000;
            border-radius: 5px;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        .status {
            background: #333;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 14px;
        }
        
        .error {
            background: #d32f2f;
            color: white;
        }
        
        .success {
            background: #388e3c;
            color: white;
        }
        
        .info {
            background: #1976d2;
            color: white;
        }
        
        input {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #555;
            border-radius: 3px;
            background: #333;
            color: white;
        }
        
        label {
            display: block;
            margin-top: 10px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>🎥 Mediasoup Camera Test</h1>
    
    <div class="container">
        <h2>RTMP Configuration</h2>
        <label for="rtmpUrl">RTMP URL:</label>
        <input type="text" id="rtmpUrl" value="rtmp://live.twitch.tv/app" placeholder="rtmp://live.twitch.tv/app">
        
        <label for="streamKey">Stream Key:</label>
        <input type="text" id="streamKey" value="live_1048713352_TbNczEfLtlt6wRdvxXLBrfqBBQ72Ax" placeholder="Your Twitch stream key">
    </div>
    
    <div class="container">
        <h2>Camera Preview</h2>
        <video id="localVideo" autoplay muted playsinline></video>
        <br>
        <button id="startCamera">📹 Start Camera</button>
        <button id="stopCamera" disabled>⏹️ Stop Camera</button>
    </div>
    
    <div class="container">
        <h2>Streaming</h2>
        <button id="startStream" disabled>🚀 Start Streaming to Twitch</button>
        <button id="stopStream" disabled>⏹️ Stop Streaming</button>
    </div>
    
    <div class="container">
        <h2>Status</h2>
        <div id="status" class="status">Ready to start...</div>
    </div>

    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <script>
        class MediasoupCameraTest {
            constructor() {
                this.socket = null;
                this.pc = null;
                this.localStream = null;
                this.sessionId = null;
                
                this.setupEventListeners();
            }
            
            setupEventListeners() {
                document.getElementById('startCamera').onclick = () => this.startCamera();
                document.getElementById('stopCamera').onclick = () => this.stopCamera();
                document.getElementById('startStream').onclick = () => this.startStreaming();
                document.getElementById('stopStream').onclick = () => this.stopStreaming();
            }
            
            log(message, type = 'info') {
                const status = document.getElementById('status');
                const timestamp = new Date().toLocaleTimeString();
                status.innerHTML = `[${timestamp}] ${message}`;
                status.className = `status ${type}`;
                console.log(`[${timestamp}] ${message}`);
            }
            
            async startCamera() {
                try {
                    this.log('🎥 Starting camera...', 'info');
                    
                    this.localStream = await navigator.mediaDevices.getUserMedia({
                        video: {
                            width: { ideal: 1280 },
                            height: { ideal: 720 },
                            frameRate: { ideal: 30 }
                        },
                        audio: true
                    });
                    
                    const video = document.getElementById('localVideo');
                    video.srcObject = this.localStream;
                    
                    document.getElementById('startCamera').disabled = true;
                    document.getElementById('stopCamera').disabled = false;
                    document.getElementById('startStream').disabled = false;
                    
                    this.log('✅ Camera started successfully', 'success');
                    
                } catch (error) {
                    this.log(`❌ Camera error: ${error.message}`, 'error');
                }
            }
            
            stopCamera() {
                if (this.localStream) {
                    this.localStream.getTracks().forEach(track => track.stop());
                    this.localStream = null;
                    
                    const video = document.getElementById('localVideo');
                    video.srcObject = null;
                }
                
                document.getElementById('startCamera').disabled = false;
                document.getElementById('stopCamera').disabled = true;
                document.getElementById('startStream').disabled = true;
                
                this.log('⏹️ Camera stopped', 'info');
            }
            
            async startStreaming() {
                try {
                    const rtmpUrl = document.getElementById('rtmpUrl').value;
                    const streamKey = document.getElementById('streamKey').value;
                    
                    if (!rtmpUrl || !streamKey) {
                        this.log('❌ Please enter RTMP URL and stream key', 'error');
                        return;
                    }
                    
                    if (!this.localStream) {
                        this.log('❌ Please start camera first', 'error');
                        return;
                    }
                    
                    this.log('🔌 Connecting to mediasoup service...', 'info');
                    
                    // Connect to mediasoup service
                    this.socket = io('http://localhost:3000');
                    
                    this.socket.on('connect', () => {
                        this.log('✅ Connected to mediasoup service', 'success');
                        this.createStreamSession(rtmpUrl, streamKey);
                    });
                    
                    this.socket.on('connect_error', (error) => {
                        this.log(`❌ Connection failed: ${error.message}`, 'error');
                    });
                    
                } catch (error) {
                    this.log(`❌ Streaming error: ${error.message}`, 'error');
                }
            }
            
            createStreamSession(rtmpUrl, streamKey) {
                this.log('📡 Creating stream session...', 'info');
                
                // Listen for session creation
                this.socket.on('stream-session-created', async ({ sessionId, success, error }) => {
                    if (success) {
                        this.sessionId = sessionId;
                        this.log(`✅ Session created: ${sessionId}`, 'success');
                        await this.setupWebRTC();
                    } else {
                        this.log(`❌ Session creation failed: ${error}`, 'error');
                    }
                });
                
                // Create the session
                this.socket.emit('start-stream', {
                    rtmpUrl,
                    streamKey,
                    roomId: 'camera-test'
                });
            }
            
            async setupWebRTC() {
                try {
                    this.log('🔗 Setting up WebRTC connection...', 'info');
                    
                    // Create peer connection
                    this.pc = new RTCPeerConnection({
                        iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
                    });
                    
                    // Add camera tracks
                    this.localStream.getTracks().forEach(track => {
                        this.pc.addTrack(track, this.localStream);
                        this.log(`✅ Added ${track.kind} track`, 'success');
                    });
                    
                    // Handle ICE candidates
                    this.pc.onicecandidate = (event) => {
                        if (event.candidate) {
                            this.socket.emit('webrtc-ice-candidate', {
                                sessionId: this.sessionId,
                                candidate: event.candidate
                            });
                        }
                    };
                    
                    // Create and send offer
                    const offer = await this.pc.createOffer();
                    await this.pc.setLocalDescription(offer);
                    
                    // Extract DTLS parameters
                    const dtlsParameters = this.extractDtlsParameters(offer.sdp);
                    
                    this.log('📤 Sending WebRTC offer...', 'info');
                    
                    // Send offer to mediasoup
                    this.socket.emit('webrtc-offer', {
                        sessionId: this.sessionId,
                        offer: { dtlsParameters }
                    });
                    
                    // Handle answer
                    this.socket.on('webrtc-answer', async ({ sessionId, answer }) => {
                        if (sessionId === this.sessionId) {
                            try {
                                this.log('📥 Received WebRTC answer, checking format...', 'info');
                                console.log('Answer received:', JSON.stringify(answer, null, 2));

                                // Check if answer is already in SDP format
                                if (answer.type && answer.sdp) {
                                    // Answer is already in SDP format
                                    this.log('📋 Answer is in SDP format, using directly', 'info');
                                    await this.pc.setRemoteDescription(answer);
                                } else {
                                    // Answer is in mediasoup transport format, convert to SDP
                                    this.log('🔄 Converting mediasoup answer to SDP format', 'info');
                                    const answerSdp = this.createAnswerSDP(answer);
                                    await this.pc.setRemoteDescription({
                                        type: 'answer',
                                        sdp: answerSdp
                                    });
                                }

                                this.log('✅ WebRTC handshake completed!', 'success');
                                this.log('🚀 Camera stream should now be live on Twitch!', 'success');

                                document.getElementById('startStream').disabled = true;
                                document.getElementById('stopStream').disabled = false;

                            } catch (error) {
                                this.log(`❌ Failed to complete WebRTC handshake: ${error.message}`, 'error');
                                console.error('WebRTC error details:', error);
                            }
                        }
                    });
                    
                } catch (error) {
                    this.log(`❌ WebRTC setup failed: ${error.message}`, 'error');
                }
            }
            
            extractDtlsParameters(sdp) {
                const lines = sdp.split('\n');
                const fingerprintLine = lines.find(line => line.trim().startsWith('a=fingerprint:'));

                if (!fingerprintLine) {
                    throw new Error('No fingerprint found in SDP');
                }

                const fingerprintContent = fingerprintLine.replace('a=fingerprint:', '').trim();
                const [algorithm, value] = fingerprintContent.split(' ');

                return {
                    role: 'client',
                    fingerprints: [{
                        algorithm: algorithm.toLowerCase(),
                        value: value.toUpperCase()
                    }]
                };
            }

            createAnswerSDP(answer) {
                const { iceParameters, iceCandidates, dtlsParameters } = answer;

                let sdp = `v=0\r\n`;
                sdp += `o=- 0 2 IN IP4 127.0.0.1\r\n`;
                sdp += `s=-\r\n`;
                sdp += `t=0 0\r\n`;
                sdp += `a=group:BUNDLE 0\r\n`;
                sdp += `a=msid-semantic: WMS\r\n`;

                // Video media section
                sdp += `m=video 9 UDP/TLS/RTP/SAVPF 96\r\n`;
                sdp += `c=IN IP4 0.0.0.0\r\n`;
                sdp += `a=rtcp:9 IN IP4 0.0.0.0\r\n`;

                // ICE parameters
                if (iceParameters) {
                    sdp += `a=ice-ufrag:${iceParameters.usernameFragment}\r\n`;
                    sdp += `a=ice-pwd:${iceParameters.password}\r\n`;
                }
                sdp += `a=ice-options:trickle\r\n`;

                // DTLS parameters
                if (dtlsParameters && dtlsParameters.fingerprints && dtlsParameters.fingerprints.length > 0) {
                    const fp = dtlsParameters.fingerprints[0];
                    sdp += `a=fingerprint:${fp.algorithm} ${fp.value}\r\n`;
                }
                sdp += `a=setup:active\r\n`;

                // Media attributes
                sdp += `a=mid:0\r\n`;
                sdp += `a=sendrecv\r\n`;
                sdp += `a=rtcp-mux\r\n`;
                sdp += `a=rtpmap:96 VP8/90000\r\n`;

                // ICE candidates - fix the format
                if (iceCandidates && iceCandidates.length > 0) {
                    iceCandidates.forEach(candidate => {
                        // Ensure all required fields are present
                        const foundation = candidate.foundation || '1';
                        const component = candidate.component || 1;
                        const protocol = candidate.protocol || 'udp';
                        const priority = candidate.priority || 2113667326;
                        const ip = candidate.ip || '127.0.0.1';
                        const port = candidate.port || 9;
                        const type = candidate.type || 'host';

                        sdp += `a=candidate:${foundation} ${component} ${protocol} ${priority} ${ip} ${port} typ ${type}\r\n`;
                    });
                } else {
                    // Add a default candidate if none provided
                    sdp += `a=candidate:1 1 udp 2113667326 127.0.0.1 9 typ host\r\n`;
                }

                return sdp;
            }
            
            stopStreaming() {
                if (this.socket) {
                    if (this.sessionId) {
                        this.socket.emit('stop-stream', { sessionId: this.sessionId });
                    }
                    this.socket.disconnect();
                    this.socket = null;
                }
                
                if (this.pc) {
                    this.pc.close();
                    this.pc = null;
                }
                
                this.sessionId = null;
                
                document.getElementById('startStream').disabled = false;
                document.getElementById('stopStream').disabled = true;
                
                this.log('⏹️ Streaming stopped', 'info');
            }
        }
        
        // Initialize the test
        const test = new MediasoupCameraTest();
    </script>
</body>
</html>
