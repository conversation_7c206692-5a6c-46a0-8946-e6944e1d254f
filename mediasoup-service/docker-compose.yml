version: '3.8'

services:
  mediasoup:
    build: .
    container_name: mediasoup-service
    ports:
      - "3000:3000"           # HTTP/WebSocket API
      - "40000-49999:40000-49999/udp"  # RTP ports for mediasoup
    environment:
      - NODE_ENV=production
      - LOG_LEVEL=info
      - MEDIASOUP_LISTEN_IP=0.0.0.0
      - MEDIASOUP_ANNOUNCED_IP=${MEDIASOUP_ANNOUNCED_IP:-127.0.0.1}
      - RTC_MIN_PORT=40000
      - RTC_MAX_PORT=49999
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
    restart: unless-stopped
    networks:
      - mediasoup-network
    
  # Optional: Redis for session management in multi-instance setup
  redis:
    image: redis:7-alpine
    container_name: mediasoup-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    restart: unless-stopped
    networks:
      - mediasoup-network

  # Optional: Nginx for load balancing multiple instances
  nginx:
    image: nginx:alpine
    container_name: mediasoup-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - mediasoup
    restart: unless-stopped
    networks:
      - mediasoup-network

volumes:
  redis-data:

networks:
  mediasoup-network:
    driver: bridge
