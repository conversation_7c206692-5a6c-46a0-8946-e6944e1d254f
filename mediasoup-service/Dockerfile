# Multi-stage build for optimized production image
FROM node:18-alpine AS builder

# Install build dependencies
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    gcc \
    libc-dev \
    linux-headers

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies (including mediasoup native compilation)
RUN npm ci --only=production

# Production stage
FROM node:18-alpine

# Install runtime dependencies for mediasoup and FFmpeg
RUN apk add --no-cache \
    ffmpeg \
    libc6-compat

# Create app user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S mediasoup -u 1001

WORKDIR /app

# Copy built node_modules from builder stage
COPY --from=builder /app/node_modules ./node_modules

# Copy application code
COPY --chown=mediasoup:nodejs . .

# Create logs directory
RUN mkdir -p logs && chown mediasoup:nodejs logs

# Expose ports
EXPOSE 3000
EXPOSE 40000-49999/udp

# Switch to non-root user
USER mediasoup

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node src/health-check.js

CMD ["npm", "start"]
