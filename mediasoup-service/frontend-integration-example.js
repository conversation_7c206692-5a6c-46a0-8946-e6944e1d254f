// Example integration for your React frontend
// Replace your existing WebRTC-to-RTMP connection code with this

import io from 'socket.io-client';

class MediasoupClient {
  constructor() {
    this.socket = null;
    this.pc = null;
    this.sessionId = null;
  }

  async connect(serviceUrl = 'http://localhost:3000') {
    this.socket = io(serviceUrl, {
      transports: ['websocket', 'polling']
    });

    return new Promise((resolve, reject) => {
      this.socket.on('connect', () => {
        console.log('✅ Connected to mediasoup service');
        resolve();
      });

      this.socket.on('connect_error', (error) => {
        console.error('❌ Connection failed:', error);
        reject(error);
      });
    });
  }

  async startStreaming(rtmpUrl, streamKey) {
    return new Promise((resolve, reject) => {
      // Listen for session creation
      this.socket.on('stream-session-created', async ({ sessionId, success, error }) => {
        if (success) {
          this.sessionId = sessionId;
          console.log(`✅ Session created: ${sessionId}`);
          
          try {
            await this.setupWebRTC();
            resolve(sessionId);
          } catch (err) {
            reject(err);
          }
        } else {
          reject(new Error(error));
        }
      });

      // Start the session
      this.socket.emit('start-stream', {
        rtmpUrl,
        streamKey,
        roomId: 'host-stream'
      });
    });
  }

  async setupWebRTC() {
    // Create peer connection
    this.pc = new RTCPeerConnection({
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' }
      ]
    });

    // Get user media (camera/screen)
    const stream = await navigator.mediaDevices.getUserMedia({
      video: {
        width: { ideal: 1920 },
        height: { ideal: 1080 },
        frameRate: { ideal: 30 }
      },
      audio: true
    });

    // Add tracks to peer connection
    stream.getTracks().forEach(track => {
      this.pc.addTrack(track, stream);
    });

    // Handle ICE candidates
    this.pc.onicecandidate = (event) => {
      if (event.candidate) {
        this.socket.emit('webrtc-ice-candidate', {
          sessionId: this.sessionId,
          candidate: event.candidate
        });
      }
    };

    // Create and send offer
    const offer = await this.pc.createOffer();
    await this.pc.setLocalDescription(offer);

    // Send offer to mediasoup
    this.socket.emit('webrtc-offer', {
      sessionId: this.sessionId,
      offer: {
        dtlsParameters: this.extractDtlsParameters(offer)
      }
    });

    // Handle answer
    return new Promise((resolve, reject) => {
      this.socket.on('webrtc-answer', async ({ sessionId, answer }) => {
        if (sessionId === this.sessionId) {
          try {
            // In a real implementation, you'd set the remote description
            // For mediasoup, the signaling is handled differently
            console.log('✅ WebRTC connection established');
            resolve();
          } catch (error) {
            reject(error);
          }
        }
      });
    });
  }

  extractDtlsParameters(offer) {
    // Extract DTLS parameters from SDP
    // This is a simplified version - you'd need proper SDP parsing
    return {
      role: 'client',
      fingerprints: [
        {
          algorithm: 'sha-256',
          value: 'mock:fingerprint' // Extract from actual SDP
        }
      ]
    };
  }

  async stopStreaming() {
    if (this.sessionId) {
      this.socket.emit('stop-stream', { sessionId: this.sessionId });
    }
    
    if (this.pc) {
      this.pc.close();
      this.pc = null;
    }
    
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  getSessionStats() {
    return fetch(`http://localhost:3000/sessions/${this.sessionId}`)
      .then(res => res.json());
  }
}

// Usage in your React component:
export default function StreamingComponent() {
  const [client] = useState(new MediasoupClient());
  const [isStreaming, setIsStreaming] = useState(false);

  const startStream = async () => {
    try {
      await client.connect();
      await client.startStreaming(
        'rtmp://live.twitch.tv/app',
        'your-stream-key'
      );
      setIsStreaming(true);
    } catch (error) {
      console.error('Streaming failed:', error);
    }
  };

  const stopStream = async () => {
    await client.stopStreaming();
    setIsStreaming(false);
  };

  return (
    <div>
      <button onClick={startStream} disabled={isStreaming}>
        Start Streaming
      </button>
      <button onClick={stopStream} disabled={!isStreaming}>
        Stop Streaming
      </button>
    </div>
  );
}
