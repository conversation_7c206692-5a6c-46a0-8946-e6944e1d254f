#!/usr/bin/env node

const http = require('http');

const options = {
  hostname: 'localhost',
  port: process.env.PORT || 3000,
  path: '/health',
  method: 'GET',
  timeout: 3000
};

const req = http.request(options, (res) => {
  if (res.statusCode === 200) {
    process.exit(0); // Healthy
  } else {
    console.error(`Health check failed with status: ${res.statusCode}`);
    process.exit(1); // Unhealthy
  }
});

req.on('error', (error) => {
  console.error('Health check error:', error.message);
  process.exit(1); // Unhealthy
});

req.on('timeout', () => {
  console.error('Health check timeout');
  req.destroy();
  process.exit(1); // Unhealthy
});

req.end();
