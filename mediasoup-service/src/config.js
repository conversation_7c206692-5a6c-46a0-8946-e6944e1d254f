const os = require('os');

module.exports = {
  // Server configuration
  server: {
    port: process.env.PORT || 3000,
    host: process.env.HOST || '0.0.0.0',
  },

  // mediasoup configuration
  mediasoup: {
    // Worker settings
    worker: {
      rtcMinPort: parseInt(process.env.RTC_MIN_PORT) || 40000,
      rtcMaxPort: parseInt(process.env.RTC_MAX_PORT) || 49999,
      logLevel: process.env.LOG_LEVEL || 'warn',
      logTags: [
        'info',
        'ice',
        'dtls',
        'rtp',
        'srtp',
        'rtcp',
        'rtx',
        'bwe',
        'score',
        'simulcast',
        'svc',
        'sctp'
      ],
    },

    // Router settings
    router: {
      mediaCodecs: [
        {
          kind: 'audio',
          mimeType: 'audio/opus',
          clockRate: 48000,
          channels: 2,
        },
        {
          kind: 'video',
          mimeType: 'video/VP8',
          clockRate: 90000,
          parameters: {
            'x-google-start-bitrate': 1000,
          },
        },
        {
          kind: 'video',
          mimeType: 'video/VP9',
          clockRate: 90000,
          parameters: {
            'profile-id': 2,
            'x-google-start-bitrate': 1000,
          },
        },
        {
          kind: 'video',
          mimeType: 'video/h264',
          clockRate: 90000,
          parameters: {
            'packetization-mode': 1,
            'profile-level-id': '4d0032',
            'level-asymmetry-allowed': 1,
            'x-google-start-bitrate': 1000,
          },
        },
        {
          kind: 'video',
          mimeType: 'video/h264',
          clockRate: 90000,
          parameters: {
            'packetization-mode': 1,
            'profile-level-id': '42e01f',
            'level-asymmetry-allowed': 1,
            'x-google-start-bitrate': 1000,
          },
        },
      ],
    },

    // WebRTC transport settings
    webRtcTransport: {
      listenIps: [
        {
          ip: process.env.MEDIASOUP_LISTEN_IP || '0.0.0.0',
          announcedIp: process.env.MEDIASOUP_ANNOUNCED_IP || undefined,
        },
      ],
      maxIncomingBitrate: 1500000,
      initialAvailableOutgoingBitrate: 1000000,
    },

    // Plain transport settings (for FFmpeg)
    plainTransport: {
      listenIp: {
        ip: '127.0.0.1',
        announcedIp: undefined,
      },
      maxSctpMessageSize: 262144,
    },
  },

  // FFmpeg configuration for RTMP streaming
  ffmpeg: {
    // FFmpeg binary path
    command: process.env.FFMPEG_PATH || 'ffmpeg',
    
    // Default streaming settings
    video: {
      codec: 'libx264',
      preset: 'ultrafast',
      tune: 'zerolatency',
      profile: 'baseline',
      level: '3.1',
      pixelFormat: 'yuv420p',
      keyframeInterval: 60,
      bitrate: '2500k',
      maxrate: '3000k',
      bufsize: '6000k',
    },
    
    audio: {
      codec: 'aac',
      bitrate: '128k',
      sampleRate: 44100,
      channels: 2,
    },
  },

  // Session management
  sessions: {
    maxDuration: 3600000, // 1 hour in milliseconds
    cleanupInterval: 300000, // 5 minutes
    maxConcurrentSessions: parseInt(process.env.MAX_SESSIONS) || 100,
  },

  // Logging
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    file: process.env.LOG_FILE || './logs/mediasoup-service.log',
  },
};
