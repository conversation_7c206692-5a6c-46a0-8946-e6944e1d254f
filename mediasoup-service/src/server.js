require('dotenv').config();

const express = require('express');
const { Server } = require('socket.io');
const http = require('http');
const cors = require('cors');
const mediasoup = require('mediasoup');
const { v4: uuidv4 } = require('uuid');

const config = require('./config');
const StreamingSession = require('./StreamingSession');
const Logger = require('./Logger');

class MediasoupService {
  constructor() {
    this.app = express();
    this.server = http.createServer(this.app);
    this.io = new Server(this.server, {
      cors: {
        origin: "*",
        methods: ["GET", "POST"]
      }
    });

    this.workers = [];
    this.sessions = new Map();
    this.logger = new Logger();
    
    this.setupExpress();
    this.setupSocketIO();
  }

  setupExpress() {
    this.app.use(cors());
    this.app.use(express.json());

    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        sessions: this.sessions.size,
        workers: this.workers.length
      });
    });

    // Sessions API
    this.app.get('/sessions', (req, res) => {
      const sessionList = Array.from(this.sessions.values()).map(session => ({
        id: session.id,
        status: session.status,
        startTime: session.startTime,
        duration: Date.now() - session.startTime,
        rtmpUrl: session.rtmpUrl ? session.rtmpUrl.replace(/\/[^\/]+$/, '/***') : null
      }));
      
      res.json({
        sessions: sessionList,
        total: sessionList.length
      });
    });

    // Session details API
    this.app.get('/sessions/:id', (req, res) => {
      const session = this.sessions.get(req.params.id);
      if (!session) {
        return res.status(404).json({ error: 'Session not found' });
      }

      res.json({
        id: session.id,
        status: session.status,
        startTime: session.startTime,
        duration: Date.now() - session.startTime,
        stats: session.getStats(),
        rtmpUrl: session.rtmpUrl ? session.rtmpUrl.replace(/\/[^\/]+$/, '/***') : null
      });
    });

    // Stop session API
    this.app.delete('/sessions/:id', async (req, res) => {
      const session = this.sessions.get(req.params.id);
      if (!session) {
        return res.status(404).json({ error: 'Session not found' });
      }

      try {
        await session.stop();
        this.sessions.delete(session.id);
        res.json({ message: 'Session stopped successfully' });
      } catch (error) {
        this.logger.error('Error stopping session:', error);
        res.status(500).json({ error: 'Failed to stop session' });
      }
    });
  }

  setupSocketIO() {
    this.io.on('connection', (socket) => {
      this.logger.info(`Client connected: ${socket.id}`);

      socket.on('start-stream', async (data) => {
        try {
          const { rtmpUrl, streamKey, roomId } = data;
          
          if (!rtmpUrl || !streamKey) {
            socket.emit('error', { message: 'RTMP URL and stream key are required' });
            return;
          }

          // Check session limits
          if (this.sessions.size >= config.sessions.maxConcurrentSessions) {
            socket.emit('error', { message: 'Maximum concurrent sessions reached' });
            return;
          }

          const sessionId = uuidv4();
          const fullRtmpUrl = `${rtmpUrl}/${streamKey}`;
          
          this.logger.info(`Creating streaming session ${sessionId} for ${rtmpUrl}`);

          const session = new StreamingSession({
            id: sessionId,
            rtmpUrl: fullRtmpUrl,
            roomId,
            worker: this.getWorker(),
            logger: this.logger
          });

          await session.initialize();
          this.sessions.set(sessionId, session);

          socket.emit('stream-session-created', {
            sessionId,
            success: true
          });

          this.logger.info(`Stream session created: ${sessionId}`);

        } catch (error) {
          this.logger.error('Error creating stream session:', error);
          socket.emit('stream-session-created', {
            success: false,
            error: error.message
          });
        }
      });

      socket.on('webrtc-offer', async (data) => {
        try {
          const { sessionId, offer } = data;
          this.logger.info(`📤 Received WebRTC offer for session ${sessionId}`);
          this.logger.info(`🔍 Offer details:`, JSON.stringify(offer, null, 2));

          const session = this.sessions.get(sessionId);

          if (!session) {
            socket.emit('error', { message: 'Session not found' });
            return;
          }

          const answer = await session.handleOffer(offer);
          this.logger.info(`📥 Sending WebRTC answer for session ${sessionId}`);
          socket.emit('webrtc-answer', { sessionId, answer });

        } catch (error) {
          this.logger.error('Error handling WebRTC offer:', error);
          socket.emit('error', { message: error.message });
        }
      });

      socket.on('webrtc-ice-candidate', async (data) => {
        try {
          const { sessionId, candidate } = data;
          const session = this.sessions.get(sessionId);

          if (!session) {
            socket.emit('error', { message: 'Session not found' });
            return;
          }

          await session.handleIceCandidate(candidate);

        } catch (error) {
          this.logger.error('Error handling ICE candidate:', error);
          socket.emit('error', { message: error.message });
        }
      });

      socket.on('produce', async (data) => {
        try {
          const { sessionId, kind, rtpParameters } = data;
          this.logger.info(`📹 Received produce request for ${kind} track in session ${sessionId}`);

          const session = this.sessions.get(sessionId);

          if (!session) {
            socket.emit('error', { message: 'Session not found' });
            return;
          }

          // Create producer and start streaming pipeline
          const producerId = await session.createProducer(kind, rtpParameters);

          socket.emit('produced', {
            sessionId,
            kind,
            producerId
          });

          this.logger.info(`✅ Producer created for ${kind} track in session ${sessionId}`);

        } catch (error) {
          this.logger.error('Error handling produce request:', error);
          socket.emit('error', { message: error.message });
        }
      });

      socket.on('start-test-pattern', async (data) => {
        try {
          const { sessionId } = data;
          this.logger.info(`🧪 Received test pattern request for session ${sessionId}`);

          const session = this.sessions.get(sessionId);

          if (!session) {
            socket.emit('error', { message: 'Session not found' });
            return;
          }

          // Start test pattern directly
          await session.startTestPatternFFmpeg();
          this.logger.info(`✅ Test pattern started for session ${sessionId}`);

          socket.emit('test-pattern-started', { sessionId, success: true });

        } catch (error) {
          this.logger.error('Error starting test pattern:', error);
          socket.emit('error', { message: error.message });
        }
      });

      socket.on('camera-frame', async (data) => {
        try {
          const { sessionId, frameData, frameNumber } = data;

          // Log occasionally to avoid spam
          if (frameNumber % 60 === 0) {
            this.logger.info(`📸 Received camera frame ${frameNumber} for session ${sessionId}`);
          }

          const session = this.sessions.get(sessionId);

          if (!session) {
            return; // Session not found, ignore frame
          }

          // Start streaming pipeline with camera frames if not already started
          if (!session.cameraStreamingActive) {
            this.logger.info(`🎬 Starting camera streaming pipeline for session ${sessionId}`);
            await session.startCameraStreamingPipeline();
            session.cameraStreamingActive = true;
          }

          // Process the camera frame (convert base64 to video data)
          await session.processCameraFrame(frameData, frameNumber);

        } catch (error) {
          this.logger.error('Error handling camera frame:', error);
        }
      });

      socket.on('get-router-capabilities', async (data) => {
        try {
          const { sessionId } = data;
          const session = this.sessions.get(sessionId);

          if (!session) {
            socket.emit('error', { message: 'Session not found' });
            return;
          }

          const routerRtpCapabilities = session.router.rtpCapabilities;
          socket.emit('router-capabilities', { sessionId, routerRtpCapabilities });

        } catch (error) {
          this.logger.error('Error getting router capabilities:', error);
          socket.emit('error', { message: error.message });
        }
      });

      socket.on('create-send-transport', async (data) => {
        try {
          const { sessionId } = data;
          const session = this.sessions.get(sessionId);

          if (!session) {
            socket.emit('error', { message: 'Session not found' });
            return;
          }

          const transportOptions = await session.createSendTransport();
          socket.emit('send-transport-created', { sessionId, transportOptions });

        } catch (error) {
          this.logger.error('Error creating send transport:', error);
          socket.emit('error', { message: error.message });
        }
      });

      socket.on('connect-send-transport', async (data) => {
        try {
          const { sessionId, dtlsParameters } = data;
          const session = this.sessions.get(sessionId);

          if (!session) {
            socket.emit('error', { message: 'Session not found' });
            return;
          }

          await session.connectSendTransport(dtlsParameters);

        } catch (error) {
          this.logger.error('Error connecting send transport:', error);
          socket.emit('error', { message: error.message });
        }
      });

      socket.on('stop-stream', async (data) => {
        try {
          const { sessionId } = data;
          const session = this.sessions.get(sessionId);

          if (!session) {
            socket.emit('stream-stopped', {
              sessionId,
              success: false,
              error: 'Session not found'
            });
            return;
          }

          await session.stop();
          this.sessions.delete(sessionId);

          socket.emit('stream-stopped', {
            sessionId,
            success: true
          });

          this.logger.info(`Stream session stopped: ${sessionId}`);

        } catch (error) {
          this.logger.error('Error stopping stream:', error);
          socket.emit('stream-stopped', {
            sessionId,
            success: false,
            error: error.message
          });
        }
      });

      socket.on('disconnect', () => {
        this.logger.info(`Client disconnected: ${socket.id}`);
      });
    });
  }

  async initializeWorkers() {
    const numWorkers = process.env.MEDIASOUP_WORKERS || require('os').cpus().length;

    this.logger.info(`Creating ${numWorkers} mediasoup workers...`);

    for (let i = 0; i < numWorkers; i++) {
      const worker = await mediasoup.createWorker({
        logLevel: 'warn',
        rtcMinPort: config.mediasoup.worker.rtcMinPort,
        rtcMaxPort: config.mediasoup.worker.rtcMaxPort,
      });

      worker.on('died', (error) => {
        this.logger.error(`mediasoup worker died:`, error);
        process.exit(1);
      });

      this.workers.push(worker);
      this.logger.info(`Worker ${i + 1} created with PID ${worker.pid}`);
    }
  }

  getWorker() {
    // Round-robin worker selection
    const worker = this.workers[Math.floor(Math.random() * this.workers.length)];
    return worker;
  }

  startCleanupTimer() {
    setInterval(() => {
      const now = Date.now();
      const expiredSessions = [];

      for (const [sessionId, session] of this.sessions) {
        const duration = now - session.startTime;
        if (duration > config.sessions.maxDuration) {
          expiredSessions.push(sessionId);
        }
      }

      expiredSessions.forEach(async (sessionId) => {
        const session = this.sessions.get(sessionId);
        if (session) {
          this.logger.info(`Cleaning up expired session: ${sessionId}`);
          try {
            await session.stop();
          } catch (error) {
            this.logger.error(`Error stopping expired session ${sessionId}:`, error);
          }
          this.sessions.delete(sessionId);
        }
      });

      if (expiredSessions.length > 0) {
        this.logger.info(`Cleaned up ${expiredSessions.length} expired sessions`);
      }
    }, config.sessions.cleanupInterval);
  }

  async start() {
    try {
      await this.initializeWorkers();
      this.startCleanupTimer();

      this.server.listen(config.server.port, config.server.host, () => {
        this.logger.info(`🚀 Mediasoup WebRTC-to-RTMP service running on ${config.server.host}:${config.server.port}`);
        this.logger.info(`📊 Health check: http://${config.server.host}:${config.server.port}/health`);
        this.logger.info(`📋 Sessions API: http://${config.server.host}:${config.server.port}/sessions`);
        this.logger.info(`🎬 Workers: ${this.workers.length}`);
        this.logger.info(`📺 Max concurrent sessions: ${config.sessions.maxConcurrentSessions}`);
      });

    } catch (error) {
      this.logger.error('Failed to start service:', error);
      console.error('Detailed error:', error);
      process.exit(1);
    }
  }

  async stop() {
    this.logger.info('Stopping mediasoup service...');

    // Stop all sessions
    for (const [sessionId, session] of this.sessions) {
      try {
        await session.stop();
      } catch (error) {
        this.logger.error(`Error stopping session ${sessionId}:`, error);
      }
    }
    this.sessions.clear();

    // Close all workers
    for (const worker of this.workers) {
      worker.close();
    }
    this.workers = [];

    // Close server
    this.server.close();
    this.logger.info('Service stopped');
  }
}

// Create and start the service
const service = new MediasoupService();

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('\nReceived SIGINT, shutting down gracefully...');
  await service.stop();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\nReceived SIGTERM, shutting down gracefully...');
  await service.stop();
  process.exit(0);
});

// Start the service
service.start().catch((error) => {
  console.error('Failed to start service:', error);
  process.exit(1);
});
