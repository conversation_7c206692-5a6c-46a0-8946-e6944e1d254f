const { spawn } = require('child_process');
const fs = require('fs').promises;
const path = require('path');
const config = require('./config');

class StreamingSession {
  constructor({ id, rtmpUrl, roomId, worker, logger }) {
    this.id = id;
    this.rtmpUrl = rtmpUrl;
    this.roomId = roomId;
    this.worker = worker;
    this.logger = logger;
    
    this.status = 'initializing';
    this.startTime = Date.now();
    
    // mediasoup objects
    this.router = null;
    this.webRtcTransport = null;
    this.plainTransport = null;
    this.producer = null;
    this.consumer = null;
    
    // FFmpeg process
    this.ffmpegProcess = null;
    
    // Stats
    this.stats = {
      bytesReceived: 0,
      packetsReceived: 0,
      framesReceived: 0,
      keyFramesReceived: 0,
    };
  }

  async initialize() {
    try {
      this.logger.info(`Initializing session ${this.id}`);
      
      // Create router
      this.router = await this.worker.createRouter({
        mediaCodecs: config.mediasoup.router.mediaCodecs,
      });

      // Create WebRTC transport for receiving media
      this.webRtcTransport = await this.router.createWebRtcTransport({
        ...config.mediasoup.webRtcTransport,
        enableUdp: true,
        enableTcp: true,
        preferUdp: true,
      });

      // Create plain transport for sending to FFmpeg
      this.plainTransport = await this.router.createPlainTransport({
        listenIp: {
          ip: '127.0.0.1',
          announcedIp: undefined,
        },
        rtcpMux: false,
        comedia: true, // Let FFmpeg connect to us
      });

      this.status = 'ready';
      this.logger.info(`Session ${this.id} initialized successfully`);

    } catch (error) {
      this.status = 'error';
      this.logger.error(`Failed to initialize session ${this.id}:`, error);
      throw error;
    }
  }

  async handleOffer(offer) {
    try {
      this.logger.info(`Handling WebRTC offer for session ${this.id}`);

      // Set remote description
      await this.webRtcTransport.connect({
        dtlsParameters: offer.dtlsParameters,
      });

      // Set up event handlers for when tracks are received
      this.webRtcTransport.on('produce', async (producer, callback) => {
        this.logger.info(`📹 Received ${producer.kind} track for session ${this.id}`);

        if (producer.kind === 'video') {
          this.producer = producer;

          // Start the streaming pipeline
          await this.startStreamingPipeline();
        }

        callback();
      });

      // For standard WebRTC connections, we need to manually create producers
      // after the DTLS handshake completes
      this.webRtcTransport.on('dtlsstatechange', async (dtlsState) => {
        this.logger.info(`🔐 DTLS state changed to: ${dtlsState} for session ${this.id}`);

        if (dtlsState === 'connected') {
          this.logger.info(`🎯 DTLS connected, attempting to create producers for session ${this.id}`);

          // Try to create producers for video tracks
          try {
            // Create a video producer with basic parameters
            const videoProducer = await this.webRtcTransport.produce({
              kind: 'video',
              rtpParameters: {
                codecs: [{
                  mimeType: 'video/VP8',
                  clockRate: 90000,
                  payloadType: 96
                }],
                headerExtensions: [],
                encodings: [{ ssrc: 1234567890 }],
                rtcp: { cname: 'video-stream' }
              }
            });

            this.logger.info(`✅ Video producer created: ${videoProducer.id}`);
            this.producer = videoProducer;

            // Start the streaming pipeline
            await this.startStreamingPipeline();

          } catch (error) {
            this.logger.error(`❌ Failed to create video producer: ${error.message}`);
            // Don't start test pattern automatically - wait for real video
          }
        }
      });

      // Note: Real streaming pipeline will start when producer is created
      this.logger.info(`✅ WebRTC transport ready for session ${this.id} - waiting for DTLS connection`);

      // Create answer
      const answer = {
        id: this.webRtcTransport.id,
        iceParameters: this.webRtcTransport.iceParameters,
        iceCandidates: this.webRtcTransport.iceCandidates,
        dtlsParameters: this.webRtcTransport.dtlsParameters,
        sctpParameters: this.webRtcTransport.sctpParameters,
      };

      this.status = 'connected';
      this.logger.info(`WebRTC transport connected for session ${this.id}`);

      return answer;

    } catch (error) {
      this.status = 'error';
      this.logger.error(`Failed to handle offer for session ${this.id}:`, error);
      throw error;
    }
  }

  async handleIceCandidate(candidate) {
    // ICE candidates are handled automatically by mediasoup
    // This method is here for compatibility with the client
    this.logger.debug(`ICE candidate received for session ${this.id}`);
  }

  async createSendTransport() {
    try {
      this.logger.info(`🚛 Creating send transport for session ${this.id}`);

      this.sendTransport = await this.router.createWebRtcTransport({
        listenIps: [{ ip: '127.0.0.1', announcedIp: null }],
        enableUdp: true,
        enableTcp: true,
        preferUdp: true,
      });

      const transportOptions = {
        id: this.sendTransport.id,
        iceParameters: this.sendTransport.iceParameters,
        iceCandidates: this.sendTransport.iceCandidates,
        dtlsParameters: this.sendTransport.dtlsParameters,
      };

      this.logger.info(`✅ Send transport created: ${this.sendTransport.id}`);
      return transportOptions;

    } catch (error) {
      this.logger.error(`Failed to create send transport for session ${this.id}:`, error);
      throw error;
    }
  }

  async connectSendTransport(dtlsParameters) {
    try {
      this.logger.info(`🔗 Connecting send transport for session ${this.id}`);

      await this.sendTransport.connect({ dtlsParameters });

      this.logger.info(`✅ Send transport connected for session ${this.id}`);

    } catch (error) {
      this.logger.error(`Failed to connect send transport for session ${this.id}:`, error);
      throw error;
    }
  }

  async createProducer(kind, rtpParameters) {
    try {
      this.logger.info(`🎬 Creating ${kind} producer for session ${this.id}`);

      // Create producer on WebRTC transport
      this.producer = await this.webRtcTransport.produce({
        kind,
        rtpParameters,
      });

      this.logger.info(`✅ Producer created for ${kind} in session ${this.id}`);

      // If this is a video producer, start the streaming pipeline
      if (kind === 'video') {
        await this.startStreamingPipeline();
      }

      return this.producer.id;

    } catch (error) {
      this.status = 'error';
      this.logger.error(`Failed to create ${kind} producer for session ${this.id}:`, error);
      throw error;
    }
  }

  async startStreamingPipeline() {
    try {
      this.logger.info(`🎬 Starting streaming pipeline for session ${this.id}`);

      // Create consumer on plain transport to get RTP stream (start paused)
      // Use router capabilities but ensure they're compatible with the producer
      const consumerRtpCapabilities = this.router.rtpCapabilities;

      this.logger.info(`🔍 Producer codec: ${this.producer.kind}, ${JSON.stringify(this.producer.rtpParameters.codecs[0])}`);

      this.consumer = await this.plainTransport.consume({
        producerId: this.producer.id,
        rtpCapabilities: consumerRtpCapabilities,
        paused: true, // Start paused, resume after FFmpeg is ready
      });

      this.logger.info(`✅ Consumer created for session ${this.id}`);

      // Get RTP port from mediasoup
      const mediasoupRtpPort = this.plainTransport.tuple.localPort;
      const mediasoupRtcpPort = this.plainTransport.rtcpTuple ? this.plainTransport.rtcpTuple.localPort : mediasoupRtpPort + 1;

      // Generate a unique port for FFmpeg
      this.ffmpegPort = 50000 + Math.floor(Math.random() * 1000);

      this.logger.info(`📡 Mediasoup RTP port: ${mediasoupRtpPort}, FFmpeg port: ${this.ffmpegPort}`);

      // Generate SDP file for FFmpeg with correct SSRC and codec info
      await this.generateSDPFile();

      // Connect mediasoup to send RTP to FFmpeg port
      await this.plainTransport.connect({
        ip: '127.0.0.1',
        port: this.ffmpegPort,
        rtcpPort: this.ffmpegPort + 1
      });

      this.logger.info(`🔗 Mediasoup will send RTP to FFmpeg on port ${this.ffmpegPort}`);

      // Start FFmpeg to convert RTP to RTMP (real video, not test pattern)
      await this.startFFmpeg();

      this.status = 'streaming';
      this.logger.info(`🚀 Real video streaming pipeline active for session ${this.id}`);

      // Start stats collection
      this.startStatsCollection();

    } catch (error) {
      this.status = 'error';
      this.logger.error(`Failed to start streaming pipeline for session ${this.id}:`, error);
      throw error;
    }
  }



  async startFFmpeg() {
    try {
      this.logger.info(`🎬 Starting FFmpeg to receive RTP on port ${this.ffmpegPort}`);

      const ffmpegArgs = [
        // Add timeout to prevent hanging
        '-timeout', '10000000', // 10 seconds in microseconds

        // Input from RTP (mediasoup will send to this port)
        '-f', 'rtp',
        '-i', `rtp://127.0.0.1:${this.ffmpegPort}`,
        
        // Video encoding
        '-c:v', config.ffmpeg.video.codec,
        '-preset', config.ffmpeg.video.preset,
        '-tune', config.ffmpeg.video.tune,
        '-profile:v', config.ffmpeg.video.profile,
        '-level', config.ffmpeg.video.level,
        '-pix_fmt', config.ffmpeg.video.pixelFormat,
        '-g', config.ffmpeg.video.keyframeInterval.toString(),
        '-keyint_min', config.ffmpeg.video.keyframeInterval.toString(),
        '-sc_threshold', '0',
        '-b:v', config.ffmpeg.video.bitrate,
        '-maxrate', config.ffmpeg.video.maxrate,
        '-bufsize', config.ffmpeg.video.bufsize,
        
        // Audio (generate silent audio for now)
        '-f', 'lavfi',
        '-i', 'anullsrc=channel_layout=stereo:sample_rate=48000',
        '-c:a', config.ffmpeg.audio.codec,
        '-b:a', config.ffmpeg.audio.bitrate,
        '-ar', config.ffmpeg.audio.sampleRate.toString(),
        '-ac', config.ffmpeg.audio.channels.toString(),
        
        // Output
        '-f', 'flv',
        '-rtmp_live', 'live',
        this.rtmpUrl
      ];

      this.logger.info(`Starting FFmpeg for session ${this.id}: ${config.ffmpeg.command} ${ffmpegArgs.join(' ')}`);

      this.ffmpegProcess = spawn(config.ffmpeg.command, ffmpegArgs, {
        stdio: ['ignore', 'pipe', 'pipe']
      });

      this.ffmpegProcess.stdout.on('data', (data) => {
        this.logger.debug(`FFmpeg stdout [${this.id}]: ${data.toString().trim()}`);
      });

      this.ffmpegProcess.stderr.on('data', (data) => {
        const output = data.toString().trim();
        this.logger.info(`FFmpeg stderr [${this.id}]: ${output}`);

        // Check for streaming indicators
        if (output.includes('fps=')) {
          this.status = 'streaming';
          this.logger.info(`✅ Real video streaming active for session ${this.id}`);
        }

        // Check for RTP connection issues
        if (output.includes('Connection refused') || output.includes('No route to host')) {
          this.logger.error(`❌ FFmpeg cannot connect to RTP port ${this.ffmpegPort}`);
        }

        // Check for timeout issues
        if (output.includes('timeout') || output.includes('timed out')) {
          this.logger.error(`⏰ FFmpeg RTP connection timeout on port ${this.ffmpegPort}`);
        }
      });

      this.ffmpegProcess.on('close', (code) => {
        this.logger.info(`FFmpeg process closed for session ${this.id} with code ${code}`);
        if (this.status === 'streaming') {
          this.status = 'stopped';
        }
      });

      // Wait a moment for FFmpeg to start listening, then resume consumer
      setTimeout(async () => {
        try {
          await this.consumer.resume();
          this.logger.info(`▶️ Consumer resumed - RTP data should now flow to FFmpeg on port ${this.ffmpegPort}`);

          // Add debugging for RTP flow
          this.rtpPacketCount = 0;
          this.consumer.on('rtp', (packet) => {
            this.rtpPacketCount++;

            if (this.rtpPacketCount % 100 === 0) {
              this.logger.info(`📦 Received ${this.rtpPacketCount} RTP packets from producer`);
            }
          });

          // Check if consumer is actually receiving data
          setTimeout(() => {
            if (!this.rtpPacketCount || this.rtpPacketCount === 0) {
              this.logger.error(`❌ No RTP packets received after 5 seconds - producer may not be sending data`);
              this.logger.info(`🔍 Producer stats: paused=${this.producer.paused}, closed=${this.producer.closed}`);
              this.logger.info(`🔍 Consumer stats: paused=${this.consumer.paused}, closed=${this.consumer.closed}`);
            } else {
              this.logger.info(`✅ RTP flow confirmed: ${this.rtpPacketCount} packets received`);
            }
          }, 5000);

        } catch (error) {
          this.logger.error(`❌ Failed to resume consumer:`, error);
        }
      }, 2000); // Wait 2 seconds for FFmpeg to be ready

      this.ffmpegProcess.on('error', (error) => {
        this.logger.error(`FFmpeg error for session ${this.id}:`, error);
        this.status = 'error';
      });

    } catch (error) {
      this.logger.error(`Failed to start FFmpeg for session ${this.id}:`, error);
      throw error;
    }
  }

  async startTestPatternFFmpeg() {
    try {
      this.logger.info(`🧪 Starting test pattern FFmpeg for session ${this.id}`);

      const ffmpegArgs = [
        // Test pattern input
        '-f', 'lavfi',
        '-i', 'testsrc=size=1920x1080:rate=30',

        // Audio input
        '-f', 'lavfi',
        '-i', 'anullsrc=channel_layout=stereo:sample_rate=48000',

        // Video encoding
        '-c:v', config.ffmpeg.video.codec,
        '-preset', config.ffmpeg.video.preset,
        '-tune', config.ffmpeg.video.tune,
        '-profile:v', config.ffmpeg.video.profile,
        '-level', config.ffmpeg.video.level,
        '-pix_fmt', config.ffmpeg.video.pixelFormat,
        '-g', config.ffmpeg.video.keyframeInterval.toString(),
        '-keyint_min', config.ffmpeg.video.keyframeInterval.toString(),
        '-sc_threshold', '0',
        '-b:v', config.ffmpeg.video.bitrate,
        '-maxrate', config.ffmpeg.video.maxrate,
        '-bufsize', config.ffmpeg.video.bufsize,

        // Audio encoding
        '-c:a', config.ffmpeg.audio.codec,
        '-b:a', config.ffmpeg.audio.bitrate,
        '-ar', config.ffmpeg.audio.sampleRate.toString(),
        '-ac', config.ffmpeg.audio.channels.toString(),

        // Output
        '-f', 'flv',
        '-rtmp_live', 'live',
        this.rtmpUrl
      ];

      this.logger.info(`Starting test pattern FFmpeg: ${config.ffmpeg.command} ${ffmpegArgs.join(' ')}`);

      this.ffmpegProcess = spawn(config.ffmpeg.command, ffmpegArgs, {
        stdio: ['ignore', 'pipe', 'pipe']
      });

      this.ffmpegProcess.stdout.on('data', (data) => {
        this.logger.debug(`FFmpeg stdout [${this.id}]: ${data.toString().trim()}`);
      });

      this.ffmpegProcess.stderr.on('data', (data) => {
        const output = data.toString().trim();
        this.logger.info(`FFmpeg stderr [${this.id}]: ${output}`);

        // Check for streaming indicators
        if (output.includes('fps=')) {
          this.status = 'streaming';
          this.logger.info(`✅ Test pattern streaming active for session ${this.id}`);
        }
      });

      this.ffmpegProcess.on('close', (code) => {
        this.logger.info(`Test pattern FFmpeg closed for session ${this.id} with code ${code}`);
      });

      this.ffmpegProcess.on('error', (error) => {
        this.logger.error(`Test pattern FFmpeg error for session ${this.id}:`, error);
      });

    } catch (error) {
      this.logger.error(`Failed to start test pattern FFmpeg for session ${this.id}:`, error);
      throw error;
    }
  }

  startStatsCollection() {
    this.statsInterval = setInterval(async () => {
      try {
        if (this.producer) {
          const stats = await this.producer.getStats();
          
          // Update stats from producer
          for (const stat of stats) {
            if (stat.type === 'inbound-rtp') {
              this.stats.bytesReceived = stat.bytesReceived || 0;
              this.stats.packetsReceived = stat.packetsReceived || 0;
              this.stats.framesReceived = stat.framesReceived || 0;
              this.stats.keyFramesReceived = stat.keyFramesReceived || 0;
            }
          }
        }
      } catch (error) {
        this.logger.error(`Error collecting stats for session ${this.id}:`, error);
      }
    }, 5000); // Collect stats every 5 seconds
  }

  getStats() {
    return {
      ...this.stats,
      duration: Date.now() - this.startTime,
      status: this.status,
    };
  }

  async stop() {
    try {
      this.logger.info(`Stopping session ${this.id}`);
      this.status = 'stopping';

      // Clear stats collection
      if (this.statsInterval) {
        clearInterval(this.statsInterval);
        this.statsInterval = null;
      }

      // Stop FFmpeg
      if (this.ffmpegProcess && !this.ffmpegProcess.killed) {
        this.ffmpegProcess.kill('SIGTERM');
        
        // Force kill after 5 seconds
        setTimeout(() => {
          if (!this.ffmpegProcess.killed) {
            this.ffmpegProcess.kill('SIGKILL');
          }
        }, 5000);
      }

      // Close mediasoup objects
      if (this.consumer) {
        this.consumer.close();
      }
      
      if (this.producer) {
        this.producer.close();
      }
      
      if (this.plainTransport) {
        this.plainTransport.close();
      }
      
      if (this.webRtcTransport) {
        this.webRtcTransport.close();
      }
      
      if (this.router) {
        this.router.close();
      }

      this.status = 'stopped';
      this.logger.info(`Session ${this.id} stopped successfully`);

    } catch (error) {
      this.logger.error(`Error stopping session ${this.id}:`, error);
      this.status = 'error';
      throw error;
    }
  }

  async generateSDPFile() {
    try {
      // Get consumer RTP parameters
      const codec = this.consumer.rtpParameters.codecs[0];
      const encoding = this.consumer.rtpParameters.encodings[0];
      const ssrc = encoding.ssrc;

      this.logger.info(`🔍 Consumer codec: ${codec.mimeType}, SSRC: ${ssrc}, PayloadType: ${codec.payloadType}`);

      // Generate SDP content
      const sdp = `v=0
o=- 0 0 IN IP4 127.0.0.1
s=MediasoupStream
c=IN IP4 127.0.0.1
t=0 0
m=video ${this.ffmpegPort} RTP/AVP ${codec.payloadType}
a=rtpmap:${codec.payloadType} ${codec.mimeType.split('/')[1]}/${codec.clockRate}
a=ssrc:${ssrc} cname:mediasoup-video
`;

      // Write SDP file
      this.sdpFilePath = path.join(__dirname, `../temp/stream-${this.id}.sdp`);

      // Ensure temp directory exists
      const tempDir = path.dirname(this.sdpFilePath);
      await fs.mkdir(tempDir, { recursive: true });

      await fs.writeFile(this.sdpFilePath, sdp);

      this.logger.info(`✅ SDP file generated: ${this.sdpFilePath}`);
      this.logger.info(`📋 SDP content:\n${sdp}`);

    } catch (error) {
      this.logger.error(`❌ Failed to generate SDP file:`, error);
      throw error;
    }
  }
}

module.exports = StreamingSession;
