const { spawn } = require('child_process');
const config = require('./config');

class StreamingSession {
  constructor({ id, rtmpUrl, roomId, worker, logger }) {
    this.id = id;
    this.rtmpUrl = rtmpUrl;
    this.roomId = roomId;
    this.worker = worker;
    this.logger = logger;
    
    this.status = 'initializing';
    this.startTime = Date.now();
    
    // mediasoup objects
    this.router = null;
    this.webRtcTransport = null;
    this.plainTransport = null;
    this.producer = null;
    this.consumer = null;
    
    // FFmpeg process
    this.ffmpegProcess = null;
    
    // Stats
    this.stats = {
      bytesReceived: 0,
      packetsReceived: 0,
      framesReceived: 0,
      keyFramesReceived: 0,
    };
  }

  async initialize() {
    try {
      this.logger.info(`Initializing session ${this.id}`);
      
      // Create router
      this.router = await this.worker.createRouter({
        mediaCodecs: config.mediasoup.router.mediaCodecs,
      });

      // Create WebRTC transport for receiving media
      this.webRtcTransport = await this.router.createWebRtcTransport({
        ...config.mediasoup.webRtcTransport,
        enableUdp: true,
        enableTcp: true,
        preferUdp: true,
      });

      // Create plain transport for sending to FFmpeg
      this.plainTransport = await this.router.createPlainTransport({
        listenIp: {
          ip: '127.0.0.1',
          announcedIp: undefined,
        },
        rtcpMux: false,
        comedia: true, // Let FFmpeg initiate the connection
      });

      this.status = 'ready';
      this.logger.info(`Session ${this.id} initialized successfully`);

    } catch (error) {
      this.status = 'error';
      this.logger.error(`Failed to initialize session ${this.id}:`, error);
      throw error;
    }
  }

  async handleOffer(offer) {
    try {
      this.logger.info(`Handling WebRTC offer for session ${this.id}`);

      // Set remote description
      await this.webRtcTransport.connect({
        dtlsParameters: offer.dtlsParameters,
      });

      // Set up event handlers for when tracks are received
      this.webRtcTransport.on('produce', async (producer, callback) => {
        this.logger.info(`📹 Received ${producer.kind} track for session ${this.id}`);

        if (producer.kind === 'video') {
          this.producer = producer;

          // Start the streaming pipeline
          await this.startStreamingPipeline();
        }

        callback();
      });

      // Wait for browser to send video tracks via 'produce' event
      this.logger.info(`⏳ Waiting for browser to send video tracks for session ${this.id}`);

      // Create answer
      const answer = {
        id: this.webRtcTransport.id,
        iceParameters: this.webRtcTransport.iceParameters,
        iceCandidates: this.webRtcTransport.iceCandidates,
        dtlsParameters: this.webRtcTransport.dtlsParameters,
        sctpParameters: this.webRtcTransport.sctpParameters,
      };

      this.status = 'connected';
      this.logger.info(`WebRTC transport connected for session ${this.id}`);

      return answer;

    } catch (error) {
      this.status = 'error';
      this.logger.error(`Failed to handle offer for session ${this.id}:`, error);
      throw error;
    }
  }

  async handleIceCandidate(candidate) {
    // ICE candidates are handled automatically by mediasoup
    // This method is here for compatibility with the client
    this.logger.debug(`ICE candidate received for session ${this.id}`);
  }

  async createProducer(kind, rtpParameters) {
    try {
      this.logger.info(`🎬 Creating ${kind} producer for session ${this.id}`);

      // Create producer on WebRTC transport
      this.producer = await this.webRtcTransport.produce({
        kind,
        rtpParameters,
      });

      this.logger.info(`✅ Producer created for ${kind} in session ${this.id}`);

      // If this is a video producer, start the streaming pipeline
      if (kind === 'video') {
        await this.startStreamingPipeline();
      }

      return this.producer.id;

    } catch (error) {
      this.status = 'error';
      this.logger.error(`Failed to create ${kind} producer for session ${this.id}:`, error);
      throw error;
    }
  }

  async startStreamingPipeline() {
    try {
      this.logger.info(`🎬 Starting streaming pipeline for session ${this.id}`);

      // Create consumer on plain transport to get RTP stream
      this.consumer = await this.plainTransport.consume({
        producerId: this.producer.id,
        rtpCapabilities: this.router.rtpCapabilities,
        paused: false,
      });

      this.logger.info(`✅ Consumer created for session ${this.id}`);

      // Get RTP port for FFmpeg
      const rtpPort = this.plainTransport.tuple.localPort;
      this.logger.info(`📡 Plain transport listening on port ${rtpPort} for FFmpeg connection`);

      // Wait a moment for consumer to be ready
      await new Promise(resolve => setTimeout(resolve, 100));

      // Start FFmpeg to convert RTP to RTMP (real video, not test pattern)
      await this.startFFmpeg();

      this.status = 'streaming';
      this.logger.info(`🚀 Real video streaming pipeline active for session ${this.id}`);

      // Start stats collection
      this.startStatsCollection();

    } catch (error) {
      this.status = 'error';
      this.logger.error(`Failed to start streaming pipeline for session ${this.id}:`, error);
      throw error;
    }
  }



  async startFFmpeg() {
    try {
      const rtpPort = this.plainTransport.tuple.localPort;
      
      const ffmpegArgs = [
        // Input from mediasoup plain transport
        '-protocol_whitelist', 'file,udp,rtp',
        '-f', 'rtp',
        '-i', `rtp://127.0.0.1:${rtpPort}`,
        
        // Video encoding
        '-c:v', config.ffmpeg.video.codec,
        '-preset', config.ffmpeg.video.preset,
        '-tune', config.ffmpeg.video.tune,
        '-profile:v', config.ffmpeg.video.profile,
        '-level', config.ffmpeg.video.level,
        '-pix_fmt', config.ffmpeg.video.pixelFormat,
        '-g', config.ffmpeg.video.keyframeInterval.toString(),
        '-keyint_min', config.ffmpeg.video.keyframeInterval.toString(),
        '-sc_threshold', '0',
        '-b:v', config.ffmpeg.video.bitrate,
        '-maxrate', config.ffmpeg.video.maxrate,
        '-bufsize', config.ffmpeg.video.bufsize,
        
        // Audio (generate silent audio for now)
        '-f', 'lavfi',
        '-i', 'anullsrc=channel_layout=stereo:sample_rate=48000',
        '-c:a', config.ffmpeg.audio.codec,
        '-b:a', config.ffmpeg.audio.bitrate,
        '-ar', config.ffmpeg.audio.sampleRate.toString(),
        '-ac', config.ffmpeg.audio.channels.toString(),
        
        // Output
        '-f', 'flv',
        '-rtmp_live', 'live',
        this.rtmpUrl
      ];

      this.logger.info(`Starting FFmpeg for session ${this.id}: ${config.ffmpeg.command} ${ffmpegArgs.join(' ')}`);

      this.ffmpegProcess = spawn(config.ffmpeg.command, ffmpegArgs, {
        stdio: ['ignore', 'pipe', 'pipe']
      });

      this.ffmpegProcess.stdout.on('data', (data) => {
        this.logger.debug(`FFmpeg stdout [${this.id}]: ${data.toString().trim()}`);
      });

      this.ffmpegProcess.stderr.on('data', (data) => {
        const output = data.toString().trim();
        this.logger.debug(`FFmpeg stderr [${this.id}]: ${output}`);
        
        // Check for streaming indicators
        if (output.includes('fps=')) {
          this.status = 'streaming';
        }
      });

      this.ffmpegProcess.on('close', (code) => {
        this.logger.info(`FFmpeg process closed for session ${this.id} with code ${code}`);
        if (this.status === 'streaming') {
          this.status = 'stopped';
        }
      });

      this.ffmpegProcess.on('error', (error) => {
        this.logger.error(`FFmpeg error for session ${this.id}:`, error);
        this.status = 'error';
      });

    } catch (error) {
      this.logger.error(`Failed to start FFmpeg for session ${this.id}:`, error);
      throw error;
    }
  }

  async startTestPatternFFmpeg() {
    try {
      this.logger.info(`🧪 Starting test pattern FFmpeg for session ${this.id}`);

      const ffmpegArgs = [
        // Test pattern input
        '-f', 'lavfi',
        '-i', 'testsrc=size=1920x1080:rate=30',

        // Audio input
        '-f', 'lavfi',
        '-i', 'anullsrc=channel_layout=stereo:sample_rate=48000',

        // Video encoding
        '-c:v', config.ffmpeg.video.codec,
        '-preset', config.ffmpeg.video.preset,
        '-tune', config.ffmpeg.video.tune,
        '-profile:v', config.ffmpeg.video.profile,
        '-level', config.ffmpeg.video.level,
        '-pix_fmt', config.ffmpeg.video.pixelFormat,
        '-g', config.ffmpeg.video.keyframeInterval.toString(),
        '-keyint_min', config.ffmpeg.video.keyframeInterval.toString(),
        '-sc_threshold', '0',
        '-b:v', config.ffmpeg.video.bitrate,
        '-maxrate', config.ffmpeg.video.maxrate,
        '-bufsize', config.ffmpeg.video.bufsize,

        // Audio encoding
        '-c:a', config.ffmpeg.audio.codec,
        '-b:a', config.ffmpeg.audio.bitrate,
        '-ar', config.ffmpeg.audio.sampleRate.toString(),
        '-ac', config.ffmpeg.audio.channels.toString(),

        // Output
        '-f', 'flv',
        '-rtmp_live', 'live',
        this.rtmpUrl
      ];

      this.logger.info(`Starting test pattern FFmpeg: ${config.ffmpeg.command} ${ffmpegArgs.join(' ')}`);

      this.ffmpegProcess = spawn(config.ffmpeg.command, ffmpegArgs, {
        stdio: ['ignore', 'pipe', 'pipe']
      });

      this.ffmpegProcess.stdout.on('data', (data) => {
        this.logger.debug(`FFmpeg stdout [${this.id}]: ${data.toString().trim()}`);
      });

      this.ffmpegProcess.stderr.on('data', (data) => {
        const output = data.toString().trim();
        this.logger.info(`FFmpeg stderr [${this.id}]: ${output}`);

        // Check for streaming indicators
        if (output.includes('fps=')) {
          this.status = 'streaming';
          this.logger.info(`✅ Test pattern streaming active for session ${this.id}`);
        }
      });

      this.ffmpegProcess.on('close', (code) => {
        this.logger.info(`Test pattern FFmpeg closed for session ${this.id} with code ${code}`);
      });

      this.ffmpegProcess.on('error', (error) => {
        this.logger.error(`Test pattern FFmpeg error for session ${this.id}:`, error);
      });

    } catch (error) {
      this.logger.error(`Failed to start test pattern FFmpeg for session ${this.id}:`, error);
      throw error;
    }
  }

  startStatsCollection() {
    this.statsInterval = setInterval(async () => {
      try {
        if (this.producer) {
          const stats = await this.producer.getStats();
          
          // Update stats from producer
          for (const stat of stats) {
            if (stat.type === 'inbound-rtp') {
              this.stats.bytesReceived = stat.bytesReceived || 0;
              this.stats.packetsReceived = stat.packetsReceived || 0;
              this.stats.framesReceived = stat.framesReceived || 0;
              this.stats.keyFramesReceived = stat.keyFramesReceived || 0;
            }
          }
        }
      } catch (error) {
        this.logger.error(`Error collecting stats for session ${this.id}:`, error);
      }
    }, 5000); // Collect stats every 5 seconds
  }

  getStats() {
    return {
      ...this.stats,
      duration: Date.now() - this.startTime,
      status: this.status,
    };
  }

  async stop() {
    try {
      this.logger.info(`Stopping session ${this.id}`);
      this.status = 'stopping';

      // Clear stats collection
      if (this.statsInterval) {
        clearInterval(this.statsInterval);
        this.statsInterval = null;
      }

      // Stop FFmpeg
      if (this.ffmpegProcess && !this.ffmpegProcess.killed) {
        this.ffmpegProcess.kill('SIGTERM');
        
        // Force kill after 5 seconds
        setTimeout(() => {
          if (!this.ffmpegProcess.killed) {
            this.ffmpegProcess.kill('SIGKILL');
          }
        }, 5000);
      }

      // Close mediasoup objects
      if (this.consumer) {
        this.consumer.close();
      }
      
      if (this.producer) {
        this.producer.close();
      }
      
      if (this.plainTransport) {
        this.plainTransport.close();
      }
      
      if (this.webRtcTransport) {
        this.webRtcTransport.close();
      }
      
      if (this.router) {
        this.router.close();
      }

      this.status = 'stopped';
      this.logger.info(`Session ${this.id} stopped successfully`);

    } catch (error) {
      this.logger.error(`Error stopping session ${this.id}:`, error);
      this.status = 'error';
      throw error;
    }
  }
}

module.exports = StreamingSession;
