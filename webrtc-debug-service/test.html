<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebRTC Debug Test</title>
    <script src="/socket.io/socket.io.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 900px; margin: 0 auto; }
        video { width: 320px; height: 240px; border: 1px solid #ccc; margin: 10px; }
        .controls { margin: 20px 0; }
        button { padding: 10px 20px; margin: 5px; font-size: 16px; }
        .log { background: #f0f0f0; padding: 10px; margin: 10px 0; height: 300px; overflow-y: scroll; font-family: monospace; font-size: 12px; }
        .stats { background: #e8f5e8; padding: 10px; margin: 10px 0; }
        .debug-info { background: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 WebRTC Debug Test</h1>
        <p>This tests the <strong>exact same WebRTC flow</strong> as your main application</p>
        
        <div class="controls">
            <button onclick="startCamera()">📹 Start Camera</button>
            <button onclick="startWebRTC()">🔗 Start WebRTC</button>
            <button onclick="getDebugInfo()">📊 Get Debug Info</button>
            <button onclick="stopWebRTC()">🛑 Stop WebRTC</button>
            <button onclick="clearLog()">🗑️ Clear Log</button>
        </div>

        <div>
            <video id="localVideo" autoplay muted playsinline></video>
            <video id="remoteVideo" autoplay playsinline></video>
        </div>

        <div class="stats" id="stats">
            <strong>Status:</strong> Ready to test WebRTC transport
        </div>

        <div class="debug-info" id="debugInfo">
            <strong>Debug Info:</strong> Click "Get Debug Info" after starting WebRTC
        </div>

        <div class="log" id="log"></div>
    </div>

    <script>
        const socket = io('http://localhost:3003');
        let localVideo = document.getElementById('localVideo');
        let remoteVideo = document.getElementById('remoteVideo');
        let logDiv = document.getElementById('log');
        let statsDiv = document.getElementById('stats');
        let debugInfoDiv = document.getElementById('debugInfo');
        
        let localStream = null;
        let peerConnection = null;
        let sessionId = null;

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function updateStats(message) {
            statsDiv.innerHTML = `<strong>Status:</strong> ${message}`;
        }

        function updateDebugInfo(info) {
            debugInfoDiv.innerHTML = `<strong>Debug Info:</strong> ${info}`;
        }

        function clearLog() {
            logDiv.innerHTML = '';
        }

        async function startCamera() {
            try {
                log('🎥 Requesting camera access...');
                updateStats('Starting camera...');
                
                localStream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 1920 },
                        height: { ideal: 1080 },
                        frameRate: { ideal: 30 }
                    },
                    audio: true
                });

                localVideo.srcObject = localStream;
                
                const videoTrack = localStream.getVideoTracks()[0];
                const settings = videoTrack.getSettings();
                
                log(`✅ Camera started: ${settings.width}x${settings.height} @ ${settings.frameRate}fps`);
                log(`📹 Video track: ${videoTrack.id}, enabled: ${videoTrack.enabled}, readyState: ${videoTrack.readyState}`);
                updateStats(`Camera active: ${settings.width}x${settings.height}`);
                
            } catch (error) {
                log(`❌ Camera error: ${error.message}`);
                updateStats('Camera failed');
            }
        }

        async function startWebRTC() {
            if (!localStream) {
                log('❌ No camera stream available. Start camera first.');
                return;
            }

            try {
                log('🔗 Starting WebRTC debug session...');
                updateStats('Starting WebRTC...');
                
                // Create peer connection (same as your main app)
                peerConnection = new RTCPeerConnection({
                    iceServers: [
                        { urls: 'stun:stun.l.google.com:19302' }
                    ]
                });

                // Add event listeners (same as your main app)
                peerConnection.onconnectionstatechange = () => {
                    log(`🔄 Connection state: ${peerConnection.connectionState}`);
                };

                peerConnection.oniceconnectionstatechange = () => {
                    log(`🧊 ICE connection state: ${peerConnection.iceConnectionState}`);
                };

                peerConnection.onicecandidate = (event) => {
                    if (event.candidate) {
                        log(`🧊 Sending ICE candidate`);
                        socket.emit('webrtc-ice-candidate', {
                            sessionId: sessionId,
                            candidate: event.candidate
                        });
                    }
                };

                peerConnection.ontrack = (event) => {
                    log(`📥 Received remote track: ${event.track.kind}`);
                    if (event.track.kind === 'video') {
                        remoteVideo.srcObject = event.streams[0];
                    }
                };

                // Add tracks to peer connection (same as your main app)
                localStream.getTracks().forEach(track => {
                    const sender = peerConnection.addTrack(track, localStream);
                    log(`✅ Added ${track.kind} track: ${track.id}`);
                });

                // Start debug session
                socket.emit('start-webrtc-debug', {});
                
            } catch (error) {
                log(`❌ WebRTC error: ${error.message}`);
                updateStats('WebRTC failed');
            }
        }

        async function createAndSendOffer() {
            try {
                log('📤 Creating WebRTC offer...');
                
                const offer = await peerConnection.createOffer();
                await peerConnection.setLocalDescription(offer);
                
                log(`✅ Created offer, SDP length: ${offer.sdp.length}`);
                log(`📤 Sending offer to debug service...`);
                
                socket.emit('webrtc-offer', {
                    sessionId: sessionId,
                    offer: offer
                });
                
            } catch (error) {
                log(`❌ Create offer error: ${error.message}`);
            }
        }

        function getDebugInfo() {
            if (!sessionId) {
                log('❌ No active WebRTC session');
                return;
            }
            
            socket.emit('get-debug-info', { sessionId });
        }

        function stopWebRTC() {
            if (sessionId) {
                socket.emit('stop-webrtc-debug', { sessionId });
            }
            
            if (peerConnection) {
                peerConnection.close();
                peerConnection = null;
            }
            
            sessionId = null;
            updateStats('WebRTC stopped');
        }

        // Socket events
        socket.on('connect', () => {
            log('✅ Connected to WebRTC debug service');
            updateStats('Connected');
        });

        socket.on('webrtc-debug-started', (data) => {
            if (data.success) {
                sessionId = data.sessionId;
                log(`✅ WebRTC debug session started: ${sessionId}`);
                updateStats(`WebRTC session: ${sessionId.substring(0, 8)}...`);
                
                // Now create and send offer
                createAndSendOffer();
                
            } else {
                log(`❌ WebRTC debug failed: ${data.error}`);
                updateStats('WebRTC debug failed');
            }
        });

        socket.on('webrtc-answer', async (data) => {
            try {
                const { sessionId: responseSessionId, answer } = data;
                if (responseSessionId === sessionId) {
                    log(`📥 Received WebRTC answer, SDP length: ${answer.sdp.length}`);
                    
                    await peerConnection.setRemoteDescription(answer);
                    log(`✅ Set remote description - WebRTC connection should be established`);
                    updateStats('WebRTC connected - video should be flowing!');
                }
            } catch (error) {
                log(`❌ Set remote description error: ${error.message}`);
            }
        });

        socket.on('debug-info', (data) => {
            if (data.error) {
                updateDebugInfo(`Error: ${data.error}`);
                return;
            }
            
            const info = `
                Status: ${data.status} | 
                Frames: ${data.frameCount} | 
                Connection: ${data.connectionState} | 
                ICE: ${data.iceConnectionState} | 
                Has Video Track: ${data.hasVideoTrack} | 
                Has MediaStream: ${data.hasMediaStream}
            `;
            
            updateDebugInfo(info);
            
            log(`📊 Debug Info:`);
            log(`   Status: ${data.status}`);
            log(`   Frame Count: ${data.frameCount}`);
            log(`   Last Frame: ${data.lastFrameTime ? new Date(data.lastFrameTime).toLocaleTimeString() : 'none'}`);
            log(`   Connection State: ${data.connectionState}`);
            log(`   ICE State: ${data.iceConnectionState}`);
            log(`   Has Video Track: ${data.hasVideoTrack}`);
            log(`   Has MediaStream: ${data.hasMediaStream}`);
            
            if (data.debugLog && data.debugLog.length > 0) {
                log(`📋 Server Debug Log:`);
                data.debugLog.forEach(entry => log(`   ${entry}`));
            }
        });

        socket.on('webrtc-debug-stopped', (data) => {
            log(`🛑 WebRTC debug session stopped: ${data.sessionId}`);
            updateStats('Session stopped');
        });

        socket.on('error', (data) => {
            log(`❌ Server error: ${data.message}`);
        });

        socket.on('disconnect', () => {
            log('❌ Disconnected from debug service');
            updateStats('Disconnected');
        });

        // Auto-start instructions
        log('🚀 WebRTC Debug Test Ready');
        log('👆 Click buttons in order: Start Camera → Start WebRTC → Get Debug Info');
        log('🎯 This tests the EXACT same WebRTC flow as your main application');
    </script>
</body>
</html>
