const express = require('express');
const { Server } = require('socket.io');
const http = require('http');
const cors = require('cors');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const wrtc = require('wrtc');

const app = express();
const server = http.createServer(app);
const io = new Server(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

app.use(cors());
app.use(express.json());
app.use(express.static(__dirname));

// Store active WebRTC sessions
const sessions = new Map();

// Create debug directory
const debugDir = path.join(__dirname, 'debug');
if (!fs.existsSync(debugDir)) {
  fs.mkdirSync(debugDir);
}

app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    sessions: sessions.size
  });
});

app.get('/sessions', (req, res) => {
  const sessionList = Array.from(sessions.values()).map(session => ({
    id: session.id,
    status: session.status,
    startTime: session.startTime,
    frameCount: session.frameCount || 0,
    lastFrameTime: session.lastFrameTime || null
  }));
  
  res.json({ sessions: sessionList });
});

io.on('connection', (socket) => {
  console.log(`🔌 Client connected: ${socket.id}`);

  socket.on('start-webrtc-debug', async (data) => {
    try {
      const sessionId = crypto.randomUUID();
      console.log(`🚀 Starting WebRTC debug session: ${sessionId}`);
      
      // Create WebRTC peer connection (same as your main service)
      const peerConnection = new wrtc.RTCPeerConnection({
        iceServers: [
          { urls: 'stun:stun.l.google.com:19302' }
        ]
      });

      const session = {
        id: sessionId,
        peerConnection,
        status: 'initializing',
        startTime: Date.now(),
        frameCount: 0,
        lastFrameTime: null,
        debugLog: []
      };

      sessions.set(sessionId, session);

      // Log WebRTC events
      peerConnection.onconnectionstatechange = () => {
        const state = peerConnection.connectionState;
        console.log(`🔄 WebRTC connection state: ${state} for session ${sessionId}`);
        session.debugLog.push(`Connection state: ${state}`);
        session.status = state;
      };

      peerConnection.oniceconnectionstatechange = () => {
        const state = peerConnection.iceConnectionState;
        console.log(`🧊 ICE connection state: ${state} for session ${sessionId}`);
        session.debugLog.push(`ICE state: ${state}`);
      };

      peerConnection.ontrack = (event) => {
        console.log(`📹 Received track: ${event.track.kind} for session ${sessionId}`);
        session.debugLog.push(`Received ${event.track.kind} track: ${event.track.id}`);
        
        if (event.track.kind === 'video') {
          console.log(`✅ Video track received! ID: ${event.track.id}, enabled: ${event.track.enabled}`);
          session.debugLog.push(`Video track enabled: ${event.track.enabled}, readyState: ${event.track.readyState}`);
          
          // Try to get track settings
          try {
            const settings = event.track.getSettings ? event.track.getSettings() : {};
            console.log(`📐 Video track settings:`, settings);
            session.debugLog.push(`Video settings: ${JSON.stringify(settings)}`);
          } catch (error) {
            console.log(`⚠️ Could not get track settings:`, error.message);
          }

          // Set up frame monitoring
          session.videoTrack = event.track;
          session.status = 'receiving-video';
          
          // Monitor track state changes
          event.track.onended = () => {
            console.log(`🛑 Video track ended for session ${sessionId}`);
            session.debugLog.push('Video track ended');
          };

          event.track.onmute = () => {
            console.log(`🔇 Video track muted for session ${sessionId}`);
            session.debugLog.push('Video track muted');
          };

          event.track.onunmute = () => {
            console.log(`🔊 Video track unmuted for session ${sessionId}`);
            session.debugLog.push('Video track unmuted');
          };

          // Try to access the MediaStream
          if (event.streams && event.streams.length > 0) {
            const stream = event.streams[0];
            console.log(`📺 MediaStream received: ${stream.id}, active: ${stream.active}`);
            session.debugLog.push(`MediaStream: ${stream.id}, active: ${stream.active}, tracks: ${stream.getTracks().length}`);
            
            session.mediaStream = stream;
            
            // Start frame counting simulation
            startFrameMonitoring(session);
          }
        }
      };

      socket.emit('webrtc-debug-started', {
        sessionId,
        success: true
      });

    } catch (error) {
      console.error('❌ Error starting WebRTC debug:', error);
      socket.emit('webrtc-debug-started', {
        success: false,
        error: error.message
      });
    }
  });

  socket.on('webrtc-offer', async (data) => {
    try {
      const { sessionId, offer } = data;
      console.log(`📤 Received WebRTC offer for session ${sessionId}`);
      
      const session = sessions.get(sessionId);
      if (!session) {
        socket.emit('error', { message: 'Session not found' });
        return;
      }

      console.log(`🔍 Offer SDP length: ${offer.sdp ? offer.sdp.length : 'no SDP'}`);
      session.debugLog.push(`Received offer, SDP length: ${offer.sdp ? offer.sdp.length : 'no SDP'}`);

      // Set remote description
      await session.peerConnection.setRemoteDescription(offer);
      console.log(`✅ Set remote description for session ${sessionId}`);
      session.debugLog.push('Set remote description');

      // Create answer
      const answer = await session.peerConnection.createAnswer();
      await session.peerConnection.setLocalDescription(answer);
      
      console.log(`📥 Created WebRTC answer for session ${sessionId}`);
      console.log(`🔍 Answer SDP length: ${answer.sdp.length}`);
      session.debugLog.push(`Created answer, SDP length: ${answer.sdp.length}`);

      socket.emit('webrtc-answer', { sessionId, answer });

    } catch (error) {
      console.error('❌ Error handling WebRTC offer:', error);
      socket.emit('error', { message: error.message });
    }
  });

  socket.on('webrtc-ice-candidate', async (data) => {
    try {
      const { sessionId, candidate } = data;
      const session = sessions.get(sessionId);

      if (!session) {
        socket.emit('error', { message: 'Session not found' });
        return;
      }

      await session.peerConnection.addIceCandidate(candidate);
      console.log(`🧊 Added ICE candidate for session ${sessionId}`);
      session.debugLog.push('Added ICE candidate');

    } catch (error) {
      console.error('❌ Error handling ICE candidate:', error);
    }
  });

  socket.on('get-debug-info', (data) => {
    const { sessionId } = data;
    const session = sessions.get(sessionId);
    
    if (!session) {
      socket.emit('debug-info', { error: 'Session not found' });
      return;
    }

    socket.emit('debug-info', {
      sessionId,
      status: session.status,
      frameCount: session.frameCount,
      lastFrameTime: session.lastFrameTime,
      debugLog: session.debugLog,
      hasVideoTrack: !!session.videoTrack,
      hasMediaStream: !!session.mediaStream,
      connectionState: session.peerConnection.connectionState,
      iceConnectionState: session.peerConnection.iceConnectionState
    });
  });

  socket.on('stop-webrtc-debug', (data) => {
    const { sessionId } = data;
    const session = sessions.get(sessionId);
    
    if (session) {
      session.peerConnection.close();
      sessions.delete(sessionId);
      console.log(`🛑 Stopped WebRTC debug session: ${sessionId}`);
    }
    
    socket.emit('webrtc-debug-stopped', { sessionId });
  });

  socket.on('disconnect', () => {
    console.log(`🔌 Client disconnected: ${socket.id}`);
  });
});

function startFrameMonitoring(session) {
  // Simulate frame monitoring since we can't easily extract frames in Node.js
  // This will at least tell us if the track is active
  const interval = setInterval(() => {
    if (session.videoTrack && session.videoTrack.readyState === 'live') {
      session.frameCount++;
      session.lastFrameTime = Date.now();
      
      if (session.frameCount % 30 === 0) {
        console.log(`📹 Session ${session.id}: Simulated ${session.frameCount} frames (track is live)`);
      }
    } else {
      console.log(`⚠️ Session ${session.id}: Video track not live (readyState: ${session.videoTrack?.readyState})`);
      clearInterval(interval);
    }
  }, 33); // ~30 FPS

  // Clean up after 30 seconds
  setTimeout(() => {
    clearInterval(interval);
  }, 30000);
}

const PORT = process.env.PORT || 3003;
server.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 WebRTC Debug Service running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`📋 Sessions API: http://localhost:${PORT}/sessions`);
  console.log(`🧪 Test page: http://localhost:${PORT}/test.html`);
});
