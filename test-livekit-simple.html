<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LiveKit Camera Test - Simple</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .video-container {
            margin: 20px 0;
        }
        video {
            width: 100%;
            max-width: 640px;
            height: auto;
            border: 2px solid #333;
            border-radius: 8px;
        }
        .controls {
            margin: 20px 0;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            font-size: 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .start-btn {
            background-color: #4CAF50;
            color: white;
        }
        .stop-btn {
            background-color: #f44336;
            color: white;
        }
        .egress-btn {
            background-color: #2196F3;
            color: white;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🎥 LiveKit Camera to Twitch Test (Simple)</h1>
    
    <div class="controls">
        <button id="startBtn" class="start-btn">📹 Start Camera</button>
        <button id="stopBtn" class="stop-btn" disabled>⏹️ Stop Camera</button>
        <button id="egressBtn" class="egress-btn" disabled>🚀 Start RTMP Stream</button>
    </div>

    <div id="status" class="status info">Ready to start camera</div>

    <div class="video-container">
        <video id="localVideo" autoplay muted playsinline></video>
    </div>

    <div class="log" id="log"></div>

    <script type="module">
        // Import LiveKit as ES module
        import { Room, createLocalVideoTrack, createLocalAudioTrack, Track } from 'https://unpkg.com/livekit-client@2.5.7/dist/livekit-client.esm.mjs';

        const log = (message) => {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('log');
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        };

        const setStatus = (message, type = 'info') => {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        };

        let room = null;
        let localVideoTrack = null;
        let localAudioTrack = null;
        let currentEgressId = null;

        // LiveKit configuration - Local fallback while cloud restarts
        const LIVEKIT_SERVICE_URL = 'http://localhost:3002';

        // Generate access token via our service
        async function getAccessToken(roomName, participantName) {
            try {
                const response = await fetch(`${LIVEKIT_SERVICE_URL}/token`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ roomName, participantName })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                return data;
            } catch (error) {
                throw new Error(`Failed to get access token: ${error.message}`);
            }
        }

        document.getElementById('startBtn').addEventListener('click', async () => {
            try {
                log('🎥 Starting camera...');
                setStatus('Starting camera...', 'info');

                // Get camera stream
                const stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 1280 },
                        height: { ideal: 720 },
                        frameRate: { ideal: 30 }
                    },
                    audio: true
                });

                // Display local video
                const localVideo = document.getElementById('localVideo');
                localVideo.srcObject = stream;

                log('✅ Camera started successfully');

                // Connect to LiveKit room
                log('🔌 Connecting to LiveKit...');
                
                room = new Room();
                
                // Get token from our service
                const tokenData = await getAccessToken('test-room', 'test-user');
                
                await room.connect(tokenData.url, tokenData.token);
                
                log('✅ Connected to LiveKit room');

                // Create and publish tracks
                log('📡 Publishing camera and audio tracks...');
                
                localVideoTrack = await createLocalVideoTrack({
                    source: Track.Source.Camera,
                    resolution: {
                        width: 1280,
                        height: 720,
                        frameRate: 30
                    }
                });
                
                localAudioTrack = await createLocalAudioTrack({
                    source: Track.Source.Microphone
                });
                
                await room.localParticipant.publishTrack(localVideoTrack);
                await room.localParticipant.publishTrack(localAudioTrack);
                
                log('✅ Camera and audio tracks published to LiveKit');
                setStatus('Camera streaming to LiveKit', 'success');

                // Enable controls
                document.getElementById('startBtn').disabled = true;
                document.getElementById('stopBtn').disabled = false;
                document.getElementById('egressBtn').disabled = false;

            } catch (error) {
                log(`❌ Error: ${error.message}`);
                setStatus(`Error: ${error.message}`, 'error');
                console.error('Full error:', error);
            }
        });

        document.getElementById('stopBtn').addEventListener('click', async () => {
            try {
                log('⏹️ Stopping camera...');
                
                if (room) {
                    await room.disconnect();
                    room = null;
                }
                
                if (localVideoTrack) {
                    localVideoTrack.stop();
                    localVideoTrack = null;
                }
                
                if (localAudioTrack) {
                    localAudioTrack.stop();
                    localAudioTrack = null;
                }

                const localVideo = document.getElementById('localVideo');
                if (localVideo.srcObject) {
                    localVideo.srcObject.getTracks().forEach(track => track.stop());
                    localVideo.srcObject = null;
                }

                log('✅ Camera stopped');
                setStatus('Camera stopped', 'info');

                // Reset controls
                document.getElementById('startBtn').disabled = false;
                document.getElementById('stopBtn').disabled = true;
                document.getElementById('egressBtn').disabled = true;

            } catch (error) {
                log(`❌ Error stopping: ${error.message}`);
            }
        });

        document.getElementById('egressBtn').addEventListener('click', async () => {
            try {
                log('🚀 Starting RTMP egress to Twitch...');
                setStatus('Starting RTMP stream...', 'info');

                // Start RTMP egress via our service
                const response = await fetch(`${LIVEKIT_SERVICE_URL}/start-stream`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ roomName: 'test-room' })
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(`HTTP ${response.status}: ${errorData.error || 'Unknown error'}`);
                }

                const data = await response.json();
                currentEgressId = data.egressId;
                
                log(`✅ RTMP egress started: ${data.egressId}`);
                log('🎯 Streaming to Twitch!');
                
                setStatus('RTMP stream to Twitch active!', 'success');

            } catch (error) {
                log(`❌ Error starting egress: ${error.message}`);
                setStatus(`Egress error: ${error.message}`, 'error');
                console.error('Full egress error:', error);
            }
        });

        log('🚀 LiveKit test page loaded (ES modules)');
    </script>
</body>
</html>
