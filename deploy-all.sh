#!/bin/bash

# Complete deployment script for WebRTC streaming platform
# Deploys TURN server, RTMP service, and main application

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID=${GOOGLE_CLOUD_PROJECT:-""}
REGION=${REGION:-"us-central1"}
ZONE=${ZONE:-"us-central1-a"}

echo -e "${BLUE}🚀 WebRTC Streaming Platform - Complete Deployment${NC}"
echo "=================================================="

# Check prerequisites
if [ -z "$PROJECT_ID" ]; then
    echo -e "${RED}❌ Error: GOOGLE_CLOUD_PROJECT environment variable not set${NC}"
    echo "Please set your Google Cloud project ID:"
    echo "export GOOGLE_CLOUD_PROJECT=\"your-project-id\""
    exit 1
fi

echo -e "${GREEN}✅ Project ID: ${PROJECT_ID}${NC}"
echo -e "${GREEN}✅ Region: ${REGION}${NC}"
echo -e "${GREEN}✅ Zone: ${ZONE}${NC}"
echo ""

# Check if gcloud is installed and authenticated
if ! command -v gcloud &> /dev/null; then
    echo -e "${RED}❌ Error: Google Cloud SDK not found${NC}"
    echo "Please install the Google Cloud SDK: https://cloud.google.com/sdk/docs/install"
    exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Error: Docker not found${NC}"
    echo "Please install Docker: https://docs.docker.com/get-docker/"
    exit 1
fi

echo -e "${GREEN}✅ Prerequisites check passed${NC}"
echo ""

# Function to deploy a service
deploy_service() {
    local service_name=$1
    local service_dir=$2
    
    echo -e "${BLUE}📦 Deploying ${service_name}...${NC}"
    echo "----------------------------------------"
    
    if [ ! -d "$service_dir" ]; then
        echo -e "${RED}❌ Error: ${service_dir} directory not found${NC}"
        return 1
    fi
    
    cd "$service_dir"
    
    if [ ! -f "deploy-gce.sh" ]; then
        echo -e "${RED}❌ Error: deploy-gce.sh not found in ${service_dir}${NC}"
        cd ..
        return 1
    fi
    
    # Make script executable
    chmod +x deploy-gce.sh
    
    # Run deployment
    if ./deploy-gce.sh; then
        echo -e "${GREEN}✅ ${service_name} deployed successfully${NC}"
        
        # Configure client if script exists
        if [ -f "configure-client.sh" ]; then
            chmod +x configure-client.sh
            echo -e "${YELLOW}🔧 Configuring client for ${service_name}...${NC}"
            if ./configure-client.sh; then
                echo -e "${GREEN}✅ ${service_name} client configuration completed${NC}"
            else
                echo -e "${YELLOW}⚠️  ${service_name} client configuration failed (non-critical)${NC}"
            fi
        fi
    else
        echo -e "${RED}❌ ${service_name} deployment failed${NC}"
        cd ..
        return 1
    fi
    
    cd ..
    echo ""
}

# Step 1: Deploy TURN Server
echo -e "${BLUE}🎯 Step 1: Deploying TURN Server${NC}"
if deploy_service "TURN Server" "turn-server"; then
    TURN_DEPLOYED=true
else
    echo -e "${RED}❌ TURN Server deployment failed. Continuing with other services...${NC}"
    TURN_DEPLOYED=false
fi

# Step 2: Deploy RTMP Service
echo -e "${BLUE}🎬 Step 2: Deploying RTMP Streaming Service${NC}"
if deploy_service "RTMP Service" "rtmp-service"; then
    RTMP_DEPLOYED=true
else
    echo -e "${RED}❌ RTMP Service deployment failed. Continuing with main application...${NC}"
    RTMP_DEPLOYED=false
fi

# Step 3: Deploy Main Application
echo -e "${BLUE}🌐 Step 3: Deploying Main Application${NC}"
echo "----------------------------------------"

if [ ! -f "deploy.sh" ]; then
    echo -e "${RED}❌ Error: deploy.sh not found in project root${NC}"
    echo "Please ensure you have a deployment script for the main application"
    MAIN_DEPLOYED=false
else
    chmod +x deploy.sh
    if ./deploy.sh; then
        echo -e "${GREEN}✅ Main application deployed successfully${NC}"
        MAIN_DEPLOYED=true
    else
        echo -e "${RED}❌ Main application deployment failed${NC}"
        MAIN_DEPLOYED=false
    fi
fi

echo ""
echo "=================================================="
echo -e "${BLUE}📋 Deployment Summary${NC}"
echo "=================================================="

if [ "$TURN_DEPLOYED" = true ]; then
    echo -e "${GREEN}✅ TURN Server: Deployed${NC}"
else
    echo -e "${RED}❌ TURN Server: Failed${NC}"
fi

if [ "$RTMP_DEPLOYED" = true ]; then
    echo -e "${GREEN}✅ RTMP Service: Deployed${NC}"
else
    echo -e "${RED}❌ RTMP Service: Failed${NC}"
fi

if [ "$MAIN_DEPLOYED" = true ]; then
    echo -e "${GREEN}✅ Main Application: Deployed${NC}"
else
    echo -e "${RED}❌ Main Application: Failed${NC}"
fi

echo ""

# Get service URLs
if [ "$TURN_DEPLOYED" = true ]; then
    TURN_IP=$(gcloud compute instances describe coturn-turn-server --zone=${ZONE} --format='get(networkInterfaces[0].accessConfigs[0].natIP)' --project=${PROJECT_ID} 2>/dev/null || echo "Unknown")
    echo -e "${BLUE}🎯 TURN Server:${NC} turn:${TURN_IP}:3478"
fi

if [ "$RTMP_DEPLOYED" = true ]; then
    RTMP_IP=$(gcloud compute instances describe rtmp-streaming-service --zone=${ZONE} --format='get(networkInterfaces[0].accessConfigs[0].natIP)' --project=${PROJECT_ID} 2>/dev/null || echo "Unknown")
    echo -e "${BLUE}🎬 RTMP Service:${NC} http://${RTMP_IP}:3002"
fi

if [ "$MAIN_DEPLOYED" = true ]; then
    MAIN_URL=$(gcloud run services describe webrtc-streaming --region=${REGION} --format='value(status.url)' --project=${PROJECT_ID} 2>/dev/null || echo "Check Cloud Run console")
    echo -e "${BLUE}🌐 Main Application:${NC} ${MAIN_URL}"
fi

echo ""
echo -e "${BLUE}🔧 Management Commands:${NC}"
echo "View all instances: gcloud compute instances list --project=${PROJECT_ID}"
echo "View Cloud Run services: gcloud run services list --project=${PROJECT_ID}"
echo ""

if [ "$TURN_DEPLOYED" = true ] && [ "$RTMP_DEPLOYED" = true ] && [ "$MAIN_DEPLOYED" = true ]; then
    echo -e "${GREEN}🎉 All services deployed successfully!${NC}"
    echo ""
    echo -e "${YELLOW}📝 Next Steps:${NC}"
    echo "1. Test WebRTC connections in your application"
    echo "2. Configure RTMP streaming with your platform credentials"
    echo "3. Monitor service health and performance"
    echo ""
    echo -e "${YELLOW}⚠️  Important Notes:${NC}"
    echo "- Services may take a few minutes to be fully ready"
    echo "- Check firewall rules if connections fail"
    echo "- Monitor costs in Google Cloud Console"
    exit 0
else
    echo -e "${YELLOW}⚠️  Partial deployment completed${NC}"
    echo "Some services failed to deploy. Check the logs above for details."
    exit 1
fi
