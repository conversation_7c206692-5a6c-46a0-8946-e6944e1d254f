require('dotenv').config();
const express = require('express');
const { Server } = require('socket.io');
const http = require('http');
const cors = require('cors');
const { spawn } = require('child_process');
const crypto = require('crypto');
const wrtc = require('wrtc');

const app = express();
const server = http.createServer(app);
const io = new Server(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

app.use(cors());
app.use(express.json());
app.use(express.static(__dirname));

// Store active sessions
const sessions = new Map();

app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    sessions: sessions.size
  });
});

io.on('connection', (socket) => {
  console.log(`🔌 Client connected: ${socket.id}`);

  socket.on('start-stream', async (data) => {
    try {
      const sessionId = crypto.randomUUID();
      console.log(`🚀 Starting direct WebRTC stream: ${sessionId}`);
      
      // Create WebRTC peer connection
      const peerConnection = new wrtc.RTCPeerConnection({
        iceServers: [
          { urls: 'stun:stun.l.google.com:19302' }
        ]
      });

      const session = {
        id: sessionId,
        peerConnection,
        status: 'initializing',
        startTime: Date.now(),
        ffmpegProcess: null
      };

      sessions.set(sessionId, session);

      // Handle WebRTC events
      peerConnection.onconnectionstatechange = () => {
        console.log(`🔄 Connection state: ${peerConnection.connectionState} for session ${sessionId}`);
        session.status = peerConnection.connectionState;
      };

      peerConnection.ontrack = (event) => {
        console.log(`📹 Received ${event.track.kind} track for session ${sessionId}`);
        
        if (event.track.kind === 'video') {
          console.log(`✅ Video track received! Starting FFmpeg...`);
          startFFmpegForSession(session, event.streams[0]);
        }
      };

      socket.emit('stream-started', {
        sessionId,
        success: true
      });

    } catch (error) {
      console.error('❌ Error starting stream:', error);
      socket.emit('stream-started', {
        success: false,
        error: error.message
      });
    }
  });

  socket.on('webrtc-offer', async (data) => {
    try {
      const { sessionId, offer } = data;
      console.log(`📤 Received offer for session ${sessionId}`);
      
      const session = sessions.get(sessionId);
      if (!session) {
        socket.emit('error', { message: 'Session not found' });
        return;
      }

      await session.peerConnection.setRemoteDescription(offer);
      const answer = await session.peerConnection.createAnswer();
      await session.peerConnection.setLocalDescription(answer);
      
      console.log(`📥 Sending answer for session ${sessionId}`);
      socket.emit('webrtc-answer', { sessionId, answer });

    } catch (error) {
      console.error('❌ Error handling offer:', error);
      socket.emit('error', { message: error.message });
    }
  });

  socket.on('webrtc-ice-candidate', async (data) => {
    try {
      const { sessionId, candidate } = data;
      const session = sessions.get(sessionId);

      if (session) {
        await session.peerConnection.addIceCandidate(candidate);
        console.log(`🧊 Added ICE candidate for session ${sessionId}`);
      }
    } catch (error) {
      console.error('❌ Error handling ICE candidate:', error);
    }
  });

  socket.on('stop-stream', (data) => {
    const { sessionId } = data;
    const session = sessions.get(sessionId);
    
    if (session) {
      if (session.ffmpegProcess) {
        session.ffmpegProcess.kill();
        console.log(`🛑 Killed FFmpeg for session ${sessionId}`);
      }
      
      session.peerConnection.close();
      sessions.delete(sessionId);
      console.log(`🛑 Stopped stream session: ${sessionId}`);
    }
    
    socket.emit('stream-stopped', { sessionId });
  });

  socket.on('disconnect', () => {
    console.log(`🔌 Client disconnected: ${socket.id}`);
  });
});

function startFFmpegForSession(session, mediaStream) {
  try {
    console.log(`🎬 Starting FFmpeg for session ${session.id}`);
    
    // Start FFmpeg with test pattern (fixed command line)
    const ffmpegArgs = [
      // Video input - test pattern
      '-f', 'lavfi',
      '-i', 'testsrc2=size=1920x1080:rate=30',

      // Audio input - synthetic audio
      '-f', 'lavfi',
      '-i', 'anullsrc=channel_layout=stereo:sample_rate=48000',

      // Video encoding
      '-c:v', 'libx264',
      '-preset', 'ultrafast',
      '-tune', 'zerolatency',
      '-profile:v', 'baseline',
      '-level', '3.1',
      '-pix_fmt', 'yuv420p',
      '-g', '60',
      '-keyint_min', '60',
      '-sc_threshold', '0',
      '-b:v', '2500k',
      '-maxrate', '3000k',
      '-bufsize', '6000k',

      // Audio encoding
      '-c:a', 'aac',
      '-b:a', '128k',
      '-ar', '44100',
      '-ac', '2',

      // Output to RTMP
      '-f', 'flv',
      '-rtmp_live', 'live',
      `${process.env.TWITCH_RTMP_URL}/${process.env.TWITCH_STREAM_KEY}`
    ];

    console.log(`🚀 Starting FFmpeg: ${process.env.FFMPEG_PATH} ${ffmpegArgs.join(' ')}`);
    
    session.ffmpegProcess = spawn(process.env.FFMPEG_PATH, ffmpegArgs);
    
    session.ffmpegProcess.stderr.on('data', (data) => {
      const output = data.toString().trim();
      console.log(`FFmpeg [${session.id}]: ${output}`);
      
      if (output.includes('fps=')) {
        console.log(`✅ FFmpeg streaming active for session ${session.id}`);
        session.status = 'streaming';
      }
    });

    session.ffmpegProcess.on('close', (code) => {
      console.log(`🛑 FFmpeg closed for session ${session.id} with code ${code}`);
      session.status = 'stopped';
    });

    session.ffmpegProcess.on('error', (error) => {
      console.error(`❌ FFmpeg error for session ${session.id}:`, error);
      session.status = 'error';
    });

  } catch (error) {
    console.error(`❌ Error starting FFmpeg for session ${session.id}:`, error);
  }
}

const PORT = process.env.PORT || 3004;
server.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Direct WebRTC Service running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`🧪 Test page: http://localhost:${PORT}/test.html`);
  console.log(`📺 RTMP URL: ${process.env.TWITCH_RTMP_URL}`);
});
