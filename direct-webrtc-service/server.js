require('dotenv').config();
const express = require('express');
const { Server } = require('socket.io');
const http = require('http');
const cors = require('cors');
const { spawn } = require('child_process');
const crypto = require('crypto');
const wrtc = require('wrtc');

const app = express();
const server = http.createServer(app);
const io = new Server(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

app.use(cors());
app.use(express.json());
app.use(express.static(__dirname));

// Store active sessions
const sessions = new Map();

app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    sessions: sessions.size
  });
});

io.on('connection', (socket) => {
  console.log(`🔌 Client connected: ${socket.id}`);

  socket.on('start-stream', async (data) => {
    try {
      const sessionId = crypto.randomUUID();
      console.log(`🚀 Starting direct WebRTC stream: ${sessionId}`);
      
      // Create WebRTC peer connection
      const peerConnection = new wrtc.RTCPeerConnection({
        iceServers: [
          { urls: 'stun:stun.l.google.com:19302' }
        ]
      });

      const session = {
        id: sessionId,
        peerConnection,
        status: 'initializing',
        startTime: Date.now(),
        ffmpegProcess: null,
        hasAudioTrack: false,
        audioChunks: []
      };

      sessions.set(sessionId, session);

      // Handle WebRTC events
      peerConnection.onconnectionstatechange = () => {
        console.log(`🔄 Connection state: ${peerConnection.connectionState} for session ${sessionId}`);
        session.status = peerConnection.connectionState;
      };

      peerConnection.ontrack = (event) => {
        console.log(`📹 Received ${event.track.kind} track for session ${sessionId}`);

        if (event.track.kind === 'video') {
          console.log(`✅ Video track received! Waiting for video frames...`);
          session.hasVideoTrack = true;
          session.videoTrack = event.track;

          // Don't start FFmpeg yet - wait for actual video frames
          console.log(`⏳ Waiting for browser to send video frames via WebSocket...`);
        } else if (event.track.kind === 'audio') {
          console.log(`✅ Audio track received! ID: ${event.track.id}`);
          session.hasAudioTrack = true;
          session.audioTrack = event.track;

          // Set up audio processing if needed
          console.log(`🎵 Audio track ready for processing`);
        }
      };

      socket.emit('stream-started', {
        sessionId,
        success: true
      });

    } catch (error) {
      console.error('❌ Error starting stream:', error);
      socket.emit('stream-started', {
        success: false,
        error: error.message
      });
    }
  });

  socket.on('webrtc-offer', async (data) => {
    try {
      const { sessionId, offer } = data;
      console.log(`📤 Received offer for session ${sessionId}`);
      
      const session = sessions.get(sessionId);
      if (!session) {
        socket.emit('error', { message: 'Session not found' });
        return;
      }

      await session.peerConnection.setRemoteDescription(offer);
      const answer = await session.peerConnection.createAnswer();
      await session.peerConnection.setLocalDescription(answer);
      
      console.log(`📥 Sending answer for session ${sessionId}`);
      socket.emit('webrtc-answer', { sessionId, answer });

    } catch (error) {
      console.error('❌ Error handling offer:', error);
      socket.emit('error', { message: error.message });
    }
  });

  socket.on('webrtc-ice-candidate', async (data) => {
    try {
      const { sessionId, candidate } = data;
      const session = sessions.get(sessionId);

      if (session) {
        await session.peerConnection.addIceCandidate(candidate);
        console.log(`🧊 Added ICE candidate for session ${sessionId}`);
      }
    } catch (error) {
      console.error('❌ Error handling ICE candidate:', error);
    }
  });

  socket.on('video-frame', (data) => {
    try {
      const { sessionId, frameData, width, height, timestamp } = data;
      const session = sessions.get(sessionId);

      if (!session) {
        console.error(`❌ Session ${sessionId} not found for video frame`);
        return;
      }

      // Start FFmpeg on first frame
      if (!session.ffmpegProcess && session.hasVideoTrack) {
        console.log(`🎬 First video frame received! Starting FFmpeg with real video...`);
        startFFmpegWithRealVideo(session, width, height);
      }

      // Send frame to FFmpeg
      if (session.ffmpegProcess && session.ffmpegProcess.stdin) {
        try {
          // Convert base64 to buffer
          const base64Data = frameData.replace(/^data:image\/\w+;base64,/, '');
          const buffer = Buffer.from(base64Data, 'base64');

          session.ffmpegProcess.stdin.write(buffer);
          session.frameCount = (session.frameCount || 0) + 1;

          // Log progress every 30 frames
          if (session.frameCount % 30 === 0) {
            console.log(`📹 Processed ${session.frameCount} real video frames for session ${sessionId}`);
          }

        } catch (error) {
          console.error(`❌ Error writing frame to FFmpeg:`, error);
        }
      }

    } catch (error) {
      console.error('❌ Error processing video frame:', error);
    }
  });

  socket.on('audio-chunk', (data) => {
    try {
      const { sessionId, audioData, timestamp } = data;
      const session = sessions.get(sessionId);

      if (!session) {
        console.error(`❌ Session ${sessionId} not found for audio chunk`);
        return;
      }

      // Store audio chunk for processing
      session.audioChunks.push({
        data: audioData,
        timestamp: timestamp
      });

      // Log audio reception
      if (session.audioChunks.length % 10 === 0) {
        console.log(`🎵 Received ${session.audioChunks.length} audio chunks for session ${sessionId}`);
      }

    } catch (error) {
      console.error('❌ Error processing audio chunk:', error);
    }
  });

  socket.on('stop-stream', (data) => {
    const { sessionId } = data;
    const session = sessions.get(sessionId);

    if (session) {
      if (session.ffmpegProcess) {
        session.ffmpegProcess.stdin.end();
        session.ffmpegProcess.kill();
        console.log(`🛑 Killed FFmpeg for session ${sessionId}`);
      }

      session.peerConnection.close();
      sessions.delete(sessionId);
      console.log(`🛑 Stopped stream session: ${sessionId}`);
    }

    socket.emit('stream-stopped', { sessionId });
  });

  socket.on('disconnect', () => {
    console.log(`🔌 Client disconnected: ${socket.id}`);
  });
});

function startFFmpegWithRealVideo(session, width, height) {
  try {
    console.log(`🎬 Starting FFmpeg with real video for session ${session.id} (${width}x${height})`);
    console.log(`🎵 Audio track available: ${session.hasAudioTrack}`);

    // Start FFmpeg with real video input from stdin
    const ffmpegArgs = [
      // Video input - raw frames from stdin
      '-f', 'image2pipe',
      '-vcodec', 'mjpeg',
      '-r', '30', // 30 FPS
      '-i', 'pipe:0',

      // Audio input - use real audio if available, otherwise synthetic
      '-f', 'lavfi',
      '-i', session.hasAudioTrack ?
        'sine=frequency=1000:sample_rate=48000' : // Placeholder for real audio
        'anullsrc=channel_layout=stereo:sample_rate=48000',

      // Video encoding
      '-c:v', 'libx264',
      '-preset', 'ultrafast',
      '-tune', 'zerolatency',
      '-profile:v', 'baseline',
      '-level', '3.1',
      '-pix_fmt', 'yuv420p',
      '-g', '60',
      '-keyint_min', '60',
      '-sc_threshold', '0',
      '-b:v', '2500k',
      '-maxrate', '3000k',
      '-bufsize', '6000k',

      // Scale video if needed
      '-vf', `scale=${width}:${height}`,

      // Audio encoding
      '-c:a', 'aac',
      '-b:a', '128k',
      '-ar', '44100',
      '-ac', '2',

      // Output to RTMP
      '-f', 'flv',
      '-rtmp_live', 'live',
      `${process.env.TWITCH_RTMP_URL}/${process.env.TWITCH_STREAM_KEY}`
    ];

    console.log(`🚀 Starting FFmpeg with real video: ${process.env.FFMPEG_PATH} ${ffmpegArgs.join(' ')}`);

    session.ffmpegProcess = spawn(process.env.FFMPEG_PATH, ffmpegArgs);
    session.frameCount = 0;

    session.ffmpegProcess.stderr.on('data', (data) => {
      const output = data.toString().trim();
      console.log(`FFmpeg [${session.id}]: ${output}`);

      if (output.includes('fps=')) {
        console.log(`✅ Real video streaming active for session ${session.id}`);
        session.status = 'streaming';
      }
    });

    session.ffmpegProcess.on('close', (code) => {
      console.log(`🛑 FFmpeg closed for session ${session.id} with code ${code}`);
      session.status = 'stopped';
    });

    session.ffmpegProcess.on('error', (error) => {
      console.error(`❌ FFmpeg error for session ${session.id}:`, error);
      session.status = 'error';
    });

    // Handle stdin errors
    session.ffmpegProcess.stdin.on('error', (error) => {
      console.error(`❌ FFmpeg stdin error for session ${session.id}:`, error);
    });

  } catch (error) {
    console.error(`❌ Error starting FFmpeg for session ${session.id}:`, error);
  }
}

const PORT = process.env.PORT || 3004;
server.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Direct WebRTC Service running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`🧪 Test page: http://localhost:${PORT}/test.html`);
  console.log(`📺 RTMP URL: ${process.env.TWITCH_RTMP_URL}`);
});
