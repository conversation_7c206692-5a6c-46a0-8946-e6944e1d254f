<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct WebRTC to RTMP Test</title>
    <script src="/socket.io/socket.io.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        video { width: 320px; height: 240px; border: 1px solid #ccc; margin: 10px; }
        .controls { margin: 20px 0; }
        button { padding: 10px 20px; margin: 5px; font-size: 16px; }
        .log { background: #f0f0f0; padding: 10px; margin: 10px 0; height: 300px; overflow-y: scroll; font-family: monospace; font-size: 12px; }
        .stats { background: #e8f5e8; padding: 10px; margin: 10px 0; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Direct WebRTC → RTMP Test</h1>
        <p>This bypasses mediasoup and sends WebRTC video directly to FFmpeg → Twitch</p>
        
        <div class="controls">
            <button onclick="startCamera()">📹 Start Camera</button>
            <button onclick="startStream()">🔴 Start Stream</button>
            <button onclick="stopStream()">⏹️ Stop Stream</button>
            <button onclick="clearLog()">🗑️ Clear Log</button>
        </div>

        <div>
            <video id="localVideo" autoplay muted playsinline></video>
        </div>

        <div class="stats" id="stats">
            <strong>Status:</strong> Ready to test direct WebRTC streaming
        </div>

        <div class="success" id="successMessage" style="display: none;">
            <strong>🎉 SUCCESS!</strong> If you see the test pattern on Twitch, the WebRTC → FFmpeg pipeline is working!
        </div>

        <div class="log" id="log"></div>
    </div>

    <script>
        const socket = io('http://localhost:3004');
        let localVideo = document.getElementById('localVideo');
        let logDiv = document.getElementById('log');
        let statsDiv = document.getElementById('stats');
        let successDiv = document.getElementById('successMessage');
        
        let localStream = null;
        let peerConnection = null;
        let sessionId = null;

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function updateStats(message) {
            statsDiv.innerHTML = `<strong>Status:</strong> ${message}`;
        }

        function clearLog() {
            logDiv.innerHTML = '';
        }

        async function startCamera() {
            try {
                log('🎥 Starting camera...');
                updateStats('Starting camera...');
                
                localStream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 1920 },
                        height: { ideal: 1080 },
                        frameRate: { ideal: 30 }
                    },
                    audio: true
                });

                localVideo.srcObject = localStream;
                
                const videoTrack = localStream.getVideoTracks()[0];
                const settings = videoTrack.getSettings();
                
                log(`✅ Camera started: ${settings.width}x${settings.height} @ ${settings.frameRate}fps`);
                updateStats(`Camera ready: ${settings.width}x${settings.height}`);
                
            } catch (error) {
                log(`❌ Camera error: ${error.message}`);
                updateStats('Camera failed');
            }
        }

        async function startStream() {
            if (!localStream) {
                log('❌ Start camera first');
                return;
            }

            try {
                log('🚀 Starting WebRTC stream...');
                updateStats('Starting stream...');
                
                // Create peer connection
                peerConnection = new RTCPeerConnection({
                    iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
                });

                // Add tracks
                localStream.getTracks().forEach(track => {
                    peerConnection.addTrack(track, localStream);
                    log(`✅ Added ${track.kind} track`);
                });

                // Handle ICE candidates
                peerConnection.onicecandidate = (event) => {
                    if (event.candidate) {
                        socket.emit('webrtc-ice-candidate', {
                            sessionId: sessionId,
                            candidate: event.candidate
                        });
                    }
                };

                // Start stream session
                socket.emit('start-stream', {});
                
            } catch (error) {
                log(`❌ Stream error: ${error.message}`);
                updateStats('Stream failed');
            }
        }

        async function createOffer() {
            try {
                const offer = await peerConnection.createOffer();
                await peerConnection.setLocalDescription(offer);
                
                log(`📤 Sending offer to server...`);
                socket.emit('webrtc-offer', {
                    sessionId: sessionId,
                    offer: offer
                });
                
            } catch (error) {
                log(`❌ Offer error: ${error.message}`);
            }
        }

        function stopStream() {
            if (sessionId) {
                socket.emit('stop-stream', { sessionId });
            }
            
            if (peerConnection) {
                peerConnection.close();
                peerConnection = null;
            }
            
            sessionId = null;
            updateStats('Stream stopped');
            successDiv.style.display = 'none';
        }

        // Socket events
        socket.on('connect', () => {
            log('✅ Connected to direct WebRTC service');
            updateStats('Connected');
        });

        socket.on('stream-started', (data) => {
            if (data.success) {
                sessionId = data.sessionId;
                log(`✅ Stream session started: ${sessionId}`);
                updateStats(`Session: ${sessionId.substring(0, 8)}...`);
                
                // Create and send offer
                createOffer();
                
            } else {
                log(`❌ Stream failed: ${data.error}`);
                updateStats('Stream failed');
            }
        });

        socket.on('webrtc-answer', async (data) => {
            try {
                const { sessionId: responseSessionId, answer } = data;
                if (responseSessionId === sessionId) {
                    log(`📥 Received answer from server`);
                    
                    await peerConnection.setRemoteDescription(answer);
                    log(`✅ WebRTC connection established!`);
                    log(`🎬 FFmpeg should now be streaming to Twitch...`);
                    updateStats('🔴 STREAMING TO TWITCH!');
                    
                    successDiv.style.display = 'block';
                }
            } catch (error) {
                log(`❌ Answer error: ${error.message}`);
            }
        });

        socket.on('stream-stopped', (data) => {
            log(`🛑 Stream stopped: ${data.sessionId}`);
            updateStats('Stream stopped');
        });

        socket.on('error', (data) => {
            log(`❌ Server error: ${data.message}`);
        });

        socket.on('disconnect', () => {
            log('❌ Disconnected from service');
            updateStats('Disconnected');
        });

        // Instructions
        log('🚀 Direct WebRTC → RTMP Test Ready');
        log('👆 Click: Start Camera → Start Stream');
        log('🎯 This will stream a TEST PATTERN to Twitch via WebRTC');
        log('📺 Check your Twitch stream to see if it works!');
    </script>
</body>
</html>
