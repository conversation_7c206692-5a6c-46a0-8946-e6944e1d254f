const express = require('express');
const cors = require('cors');
const { AccessToken, EgressClient } = require('livekit-server-sdk');

const app = express();
const port = 3002;

app.use(cors());
app.use(express.json());

// Cloud configuration with current IP
const LIVEKIT_URL = 'http://localhost:7880';
const LIVEKIT_WS_URL = 'ws://************:7880';  // Use external IP for WebSocket
const API_KEY = 'APIUQUwG76Wo6Xd';
const API_SECRET = 'wqVmuTKidmOdyAD9bbJAoWpucQrMfDEB6MZIiydiIfq';
const TWITCH_STREAM_KEY = 'live_1048713352_TbNczEfLtlt6wRdvxXLBrfqBBQ72Ax';

const egressClient = new EgressClient(LIVEKIT_URL, API_KEY, API_SECRET);

app.post('/token', async (req, res) => {
  try {
    const { roomName, participantName } = req.body;
    
    if (!roomName || !participantName) {
      return res.status(400).json({ error: 'roomName and participantName are required' });
    }

    const token = new AccessToken(API_KEY, API_SECRET, {
      identity: participantName,
      ttl: '1h',
    });

    token.addGrant({
      room: roomName,
      roomJoin: true,
      canPublish: true,
      canSubscribe: true,
    });

    const jwt = await token.toJwt();
    
    console.log(`✅ Generated token for ${participantName} in room ${roomName}`);
    
    res.json({ 
      token: jwt,
      url: LIVEKIT_WS_URL,
      roomName,
      participantName
    });
  } catch (error) {
    console.error('❌ Error generating token:', error);
    res.status(500).json({ error: 'Failed to generate token' });
  }
});

app.post('/start-stream', async (req, res) => {
  try {
    const { roomName } = req.body;
    
    if (!roomName) {
      return res.status(400).json({ error: 'roomName is required' });
    }

    console.log(`🚀 Starting RTMP egress for room: ${roomName}`);

    const egressRequest = {
      roomName: roomName,
      rtmp: {
        urls: [`rtmp://live.twitch.tv/app/${TWITCH_STREAM_KEY}`]
      },
      video: {
        codec: 'H264_BASELINE',
        width: 1920,
        height: 1080,
        framerate: 30,
        bitrate: 2500000,
      },
      audio: {
        codec: 'AAC',
        bitrate: 128000,
        frequency: 48000,
        channels: 2,
      }
    };

    const egress = await egressClient.startRoomCompositeEgress(egressRequest);
    
    console.log(`✅ RTMP egress started:`, egress);
    
    res.json({ 
      success: true, 
      egressId: egress.egressId,
      status: egress.status,
      message: 'RTMP stream to Twitch started successfully'
    });

  } catch (error) {
    console.error('❌ Error starting RTMP egress:', error);
    res.status(500).json({ 
      error: 'Failed to start RTMP stream',
      details: error.message 
    });
  }
});

app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    livekit_url: LIVEKIT_URL,
    livekit_ws_url: LIVEKIT_WS_URL,
    timestamp: new Date().toISOString()
  });
});

app.listen(port, '0.0.0.0', () => {
  console.log(`🚀 LiveKit RTMP service running on http://0.0.0.0:${port}`);
  console.log(`📡 LiveKit Server: ${LIVEKIT_URL}`);
  console.log(`🔗 LiveKit WebSocket: ${LIVEKIT_WS_URL}`);
  console.log(`🔑 API Key: ${API_KEY}`);
});
