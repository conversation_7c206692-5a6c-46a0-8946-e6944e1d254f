#!/bin/bash

# Local development startup script
# Starts all services for local WebRTC development

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting WebRTC Development Environment${NC}"
echo "=============================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  Docker is not running. Starting RTMP service with Node.js instead...${NC}"
    USE_DOCKER=false
else
    USE_DOCKER=true
fi

# Function to check if port is in use
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Check ports
echo "🔍 Checking ports..."

if check_port 3001; then
    echo -e "${YELLOW}⚠️  Port 3001 is already in use (signaling server)${NC}"
fi

if check_port 3002; then
    echo -e "${YELLOW}⚠️  Port 3002 is already in use (RTMP service)${NC}"
fi

if check_port 5173; then
    echo -e "${YELLOW}⚠️  Port 5173 is already in use (Vite dev server)${NC}"
fi

# Setup environment files
echo "📝 Setting up environment configuration..."

# Create main .env file
cat > .env << EOF
# WebRTC Configuration
VITE_TURN_SERVER=
VITE_TURN_USERNAME=webrtc
VITE_TURN_PASSWORD=webrtc123

# RTMP Service Configuration
VITE_RTMP_SERVICE_URL=http://localhost:3002
RTMP_SERVICE_URL=http://localhost:3002
EOF

# Create server .env file
mkdir -p server
cat > server/.env << EOF
# RTMP Service Configuration
RTMP_SERVICE_URL=http://localhost:3002
EOF

echo -e "${GREEN}✅ Environment files configured${NC}"

# Start RTMP service
echo "🎬 Starting RTMP service..."

cd rtmp-service

if [ "$USE_DOCKER" = true ]; then
    echo "   Using Docker Compose..."
    docker-compose up -d
    echo -e "${GREEN}✅ RTMP service started with Docker${NC}"
else
    echo "   Installing dependencies..."
    npm install > /dev/null 2>&1
    echo "   Starting with Node.js..."
    npm start &
    RTMP_PID=$!
    echo -e "${GREEN}✅ RTMP service started with Node.js (PID: $RTMP_PID)${NC}"
fi

cd ..

# Wait for RTMP service to be ready
echo "⏳ Waiting for RTMP service to be ready..."
for i in {1..30}; do
    if curl -s http://localhost:3002/health > /dev/null 2>&1; then
        echo -e "${GREEN}✅ RTMP service is ready${NC}"
        break
    fi
    sleep 1
    if [ $i -eq 30 ]; then
        echo -e "${YELLOW}⚠️  RTMP service may not be ready yet${NC}"
    fi
done

# Install server dependencies if needed
echo "📦 Checking server dependencies..."
cd server
if [ ! -d "node_modules" ] || [ ! -f "node_modules/socket.io-client/package.json" ]; then
    echo "   Installing server dependencies..."
    npm install
fi
cd ..

# Install main dependencies if needed
echo "📦 Checking main dependencies..."
if [ ! -d "node_modules" ]; then
    echo "   Installing main dependencies..."
    npm install
fi

echo ""
echo -e "${BLUE}🎉 Development environment is ready!${NC}"
echo "=================================="
echo ""
echo -e "${GREEN}Services:${NC}"
echo "🎬 RTMP Service:     http://localhost:3002"
echo "🔧 Health Check:     http://localhost:3002/health"
echo "📊 Sessions API:     http://localhost:3002/sessions"
echo ""
echo -e "${GREEN}Next Steps:${NC}"
echo "1. Start the signaling server: cd server && npm start"
echo "2. Start the frontend:         npm run dev"
echo "3. Open your browser:          http://localhost:5173"
echo ""
echo -e "${GREEN}Testing:${NC}"
echo "Test RTMP service: cd rtmp-service && node test-service.js"
echo ""
echo -e "${YELLOW}To stop services:${NC}"
if [ "$USE_DOCKER" = true ]; then
    echo "cd rtmp-service && docker-compose down"
else
    echo "kill $RTMP_PID  # Stop RTMP service"
fi
echo "Ctrl+C in other terminals to stop signaling server and frontend"
