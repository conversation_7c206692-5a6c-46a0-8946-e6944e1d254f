const express = require('express');
const cors = require('cors');
const { AccessToken, EgressClient, RoomServiceClient } = require('livekit-server-sdk');

const app = express();
const port = 3002;

// Middleware
app.use(cors());
app.use(express.json());

// LiveKit configuration
const LIVEKIT_URL = 'http://localhost:7880';
const API_KEY = 'dfnnveRi/4DyZgpLN4ikfIhkosYYb3VJLG9vzdF9T+4=';
const API_SECRET = 'fg//0qzGwi0E0gfDyhUemmOFuFcq/Wrz7OzlzRJXdzs=';

// Your Twitch stream key (replace with your actual key)
const TWITCH_STREAM_KEY = 'live_1048713352_TbNczEfLtlt6wRdvxXLBrfqBBQ72Ax';

// Initialize LiveKit clients
const egressClient = new EgressClient(LIVEKIT_URL, API_KEY, API_SECRET);
const roomClient = new RoomServiceClient(LIVEKIT_URL, API_KEY, API_SECRET);

// Generate access token for client
app.post('/token', (req, res) => {
  try {
    const { roomName, participantName } = req.body;
    
    if (!roomName || !participantName) {
      return res.status(400).json({ error: 'roomName and participantName are required' });
    }

    const token = new AccessToken(API_KEY, API_SECRET, {
      identity: participantName,
      ttl: '1h',
    });

    token.addGrant({
      room: roomName,
      roomJoin: true,
      canPublish: true,
      canSubscribe: true,
    });

    const jwt = token.toJwt();
    
    console.log(`✅ Generated token for ${participantName} in room ${roomName}`);
    
    res.json({ 
      token: jwt,
      url: LIVEKIT_URL.replace('http://', 'ws://'),
      roomName,
      participantName
    });
  } catch (error) {
    console.error('❌ Error generating token:', error);
    res.status(500).json({ error: 'Failed to generate token' });
  }
});

// Start RTMP egress to Twitch
app.post('/start-stream', async (req, res) => {
  try {
    const { roomName } = req.body;
    
    if (!roomName) {
      return res.status(400).json({ error: 'roomName is required' });
    }

    console.log(`🚀 Starting RTMP egress for room: ${roomName}`);

    // Create RTMP egress request
    const egressRequest = {
      roomName: roomName,
      rtmp: {
        urls: [`rtmp://live.twitch.tv/app/${TWITCH_STREAM_KEY}`]
      },
      video: {
        codec: 'H264_BASELINE',
        width: 1920,
        height: 1080,
        framerate: 30,
        bitrate: 2500000, // 2.5 Mbps
      },
      audio: {
        codec: 'AAC',
        bitrate: 128000,  // 128 kbps
        frequency: 48000,
        channels: 2,
      }
    };

    const egress = await egressClient.startRoomCompositeEgress(egressRequest);
    
    console.log(`✅ RTMP egress started:`, egress);
    
    res.json({ 
      success: true, 
      egressId: egress.egressId,
      status: egress.status,
      message: 'RTMP stream to Twitch started successfully'
    });

  } catch (error) {
    console.error('❌ Error starting RTMP egress:', error);
    res.status(500).json({ 
      error: 'Failed to start RTMP stream',
      details: error.message 
    });
  }
});

// Stop RTMP egress
app.post('/stop-stream', async (req, res) => {
  try {
    const { egressId } = req.body;
    
    if (!egressId) {
      return res.status(400).json({ error: 'egressId is required' });
    }

    console.log(`⏹️ Stopping RTMP egress: ${egressId}`);

    const egress = await egressClient.stopEgress(egressId);
    
    console.log(`✅ RTMP egress stopped:`, egress);
    
    res.json({ 
      success: true, 
      egressId: egress.egressId,
      status: egress.status,
      message: 'RTMP stream stopped successfully'
    });

  } catch (error) {
    console.error('❌ Error stopping RTMP egress:', error);
    res.status(500).json({ 
      error: 'Failed to stop RTMP stream',
      details: error.message 
    });
  }
});

// Get egress status
app.get('/stream-status/:egressId', async (req, res) => {
  try {
    const { egressId } = req.params;
    
    const egress = await egressClient.listEgress({ egressId });
    
    res.json({ 
      success: true, 
      egress: egress[0] || null
    });

  } catch (error) {
    console.error('❌ Error getting egress status:', error);
    res.status(500).json({ 
      error: 'Failed to get stream status',
      details: error.message 
    });
  }
});

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    livekit_url: LIVEKIT_URL,
    timestamp: new Date().toISOString()
  });
});

// List active rooms
app.get('/rooms', async (req, res) => {
  try {
    const rooms = await roomClient.listRooms();
    res.json({ rooms });
  } catch (error) {
    console.error('❌ Error listing rooms:', error);
    res.status(500).json({ error: 'Failed to list rooms' });
  }
});

app.listen(port, () => {
  console.log(`🚀 LiveKit RTMP service running on http://localhost:${port}`);
  console.log(`📊 Health check: http://localhost:${port}/health`);
  console.log(`🎬 LiveKit URL: ${LIVEKIT_URL}`);
  console.log(`🔑 API Key: ${API_KEY.substring(0, 10)}...`);
});
