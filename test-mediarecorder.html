<!DOCTYPE html>
<html>
<head>
    <title>MediaRecorder Format Test</title>
</head>
<body>
    <h1>MediaRecorder Browser Support Test</h1>
    <div id="results"></div>
    
    <script>
        function testMediaRecorderSupport() {
            const results = document.getElementById('results');
            
            // Test formats in order of preference
            const formats = [
                'video/webm;codecs=vp9,opus',
                'video/webm;codecs=vp8,opus', 
                'video/webm',
                'video/mp4;codecs=h264,aac',
                'video/mp4'
            ];
            
            results.innerHTML = '<h2>Browser: ' + navigator.userAgent + '</h2>';
            
            formats.forEach(format => {
                const supported = MediaRecorder.isTypeSupported(format);
                const status = supported ? '✅ SUPPORTED' : '❌ NOT SUPPORTED';
                results.innerHTML += `<p>${status}: ${format}</p>`;
            });
            
            // Detect optimal format
            let optimalFormat = '';
            let container = '';
            
            for (const format of formats) {
                if (MediaRecorder.isTypeSupported(format)) {
                    optimalFormat = format;
                    if (format.includes('webm')) {
                        container = 'webm';
                    } else if (format.includes('mp4')) {
                        container = 'mp4';
                    }
                    break;
                }
            }
            
            if (optimalFormat) {
                results.innerHTML += `<h3>🎯 Optimal Format: ${optimalFormat} (${container})</h3>`;
            } else {
                results.innerHTML += `<h3>⚠️ No supported formats found</h3>`;
            }
        }
        
        // Run test when page loads
        testMediaRecorderSupport();
    </script>
</body>
</html>
