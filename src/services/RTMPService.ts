import { io, Socket } from 'socket.io-client';

interface RTMPServiceConfig {
  url: string;
  streamKey: string;
}

interface RTMPSession {
  sessionId: string;
  isConnected: boolean;
  isStreaming: boolean;
}

export class RTMPService {
  private socket: Socket | null = null;
  private session: RTMPSession | null = null;
  private serviceUrl: string;
  private peerConnection: RTCPeerConnection | null = null;
  private onStatusChange?: (status: { streaming: boolean; message: string }) => void;

  constructor(serviceUrl: string) {
    this.serviceUrl = serviceUrl;
  }

  /**
   * Set callback for status changes
   */
  setStatusCallback(callback: (status: { streaming: boolean; message: string }) => void) {
    this.onStatusChange = callback;
  }

  /**
   * Connect to the RTMP service
   */
  async connect(): Promise<void> {
    if (this.socket?.connected) {
      console.log('🔌 Already connected to RTMP service');
      return;
    }

    console.log(`🔌 Connecting to RTMP service at ${this.serviceUrl}`);

    this.socket = io(this.serviceUrl, {
      transports: ['websocket', 'polling'],
      timeout: 10000
    });

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('RTMP service connection timeout'));
      }, 10000);

      this.socket!.on('connect', () => {
        clearTimeout(timeout);
        console.log('✅ Connected to RTMP service');
        console.log(`🆔 Socket ID: ${this.socket!.id}`);
        resolve();
      });

      this.socket!.on('connect_error', (error) => {
        clearTimeout(timeout);
        console.error('❌ RTMP service connection error:', error);
        reject(new Error(`RTMP service connection failed: ${error.message}`));
      });

      this.socket!.on('disconnect', (reason) => {
        console.log(`🔌 Disconnected from RTMP service: ${reason}`);
        this.cleanup();
      });

      // Add debugging for all events
      this.socket!.onAny((eventName, ...args) => {
        console.log(`📡 Socket event: ${eventName}`, args);
      });
    });
  }

  /**
   * Start streaming to RTMP endpoint
   */
  async startStreaming(config: RTMPServiceConfig): Promise<void> {
    if (!this.socket?.connected) {
      throw new Error('Not connected to RTMP service');
    }

    console.log(`🚀 Starting RTMP stream to ${config.url}`);

    // Request stream session
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Stream session creation timeout'));
      }, 15000);

      this.socket!.emit('start-stream', {
        rtmpUrl: config.url,
        streamKey: config.streamKey,
        roomId: 'host-stream' // Could be dynamic based on room
      });

      this.socket!.on('stream-session-created', ({ sessionId, success, error }) => {
        clearTimeout(timeout);
        
        if (success) {
          this.session = {
            sessionId,
            isConnected: false,
            isStreaming: false
          };
          console.log(`✅ RTMP session created: ${sessionId}`);
          resolve();
        } else {
          console.error(`❌ Failed to create RTMP session: ${error}`);
          reject(new Error(`Failed to create RTMP session: ${error}`));
        }
      });
    });
  }

  /**
   * Setup video streaming via WebRTC
   */
  async setupVideoStreaming(canvas: HTMLCanvasElement): Promise<void> {
    if (!this.session) {
      throw new Error('No active RTMP session');
    }

    console.log(`🎥 Setting up WebRTC streaming for session ${this.session.sessionId}`);
    console.log(`📐 Canvas dimensions: ${canvas.width}x${canvas.height}`);

    // Verify canvas is ready
    if (canvas.width === 0 || canvas.height === 0) {
      throw new Error('Canvas has invalid dimensions');
    }

    // Get media stream from canvas
    const stream = canvas.captureStream(30); // 30 FPS

    // Start WebRTC streaming
    await this.startWebRTCStreaming(stream);

    // Update session state
    if (this.session) {
      this.session.isConnected = true;
      this.session.isStreaming = true;
    }

    // Notify status change
    if (this.onStatusChange) {
      this.onStatusChange({
        streaming: true,
        message: 'Streaming via WebRTC to RTMP service'
      });
    }
  }

  /**
   * Start WebRTC streaming
   */
  private async startWebRTCStreaming(stream: MediaStream): Promise<void> {
    if (!this.socket || !this.session) {
      console.error('❌ Cannot start WebRTC: missing socket or session');
      return;
    }

    console.log(`🎬 Starting WebRTC streaming`);
    console.log(`🔌 Socket connected: ${this.socket.connected}`);
    console.log(`📺 Session ID: ${this.session.sessionId}`);

    // Create RTCPeerConnection
    this.peerConnection = new RTCPeerConnection({
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' }
      ]
    });

    // Add stream tracks to peer connection
    stream.getTracks().forEach(track => {
      if (this.peerConnection) {
        this.peerConnection.addTrack(track, stream);
        console.log(`➕ Added ${track.kind} track to peer connection`);
      }
    });

    // Handle ICE candidates
    this.peerConnection.onicecandidate = (event) => {
      if (event.candidate && this.socket && this.session) {
        this.socket.emit('webrtc-ice-candidate', {
          sessionId: this.session.sessionId,
          candidate: event.candidate
        });
      }
    };

    // Handle connection state changes
    this.peerConnection.onconnectionstatechange = () => {
      if (this.peerConnection) {
        console.log(`🔄 WebRTC connection state: ${this.peerConnection.connectionState}`);

        if (this.peerConnection.connectionState === 'connected') {
          console.log(`✅ WebRTC connection established`);
        } else if (this.peerConnection.connectionState === 'failed' ||
                   this.peerConnection.connectionState === 'disconnected') {
          console.log(`❌ WebRTC connection failed/disconnected`);
          this.handleDisconnection();
        }
      }
    };

    // Create and send offer
    const offer = await this.peerConnection.createOffer();
    await this.peerConnection.setLocalDescription(offer);

    console.log(`📤 Sending WebRTC offer`);

    // Extract DTLS parameters for mediasoup
    const dtlsParameters = this.extractDtlsParameters(offer);

    this.socket.emit('webrtc-offer', {
      sessionId: this.session.sessionId,
      offer: {
        dtlsParameters: dtlsParameters
      }
    });

    // Listen for answer
    this.socket.on('webrtc-answer', async ({ sessionId, answer }) => {
      if (sessionId === this.session?.sessionId && this.peerConnection) {
        console.log(`📨 Received WebRTC answer from mediasoup`);

        // Mediasoup returns transport parameters, not a standard SDP answer
        // For mediasoup, we need to handle the transport connection differently
        console.log('✅ Mediasoup transport ready for streaming');

        // The actual media streaming will be handled by mediasoup internally
        // We just need to confirm the connection is established
        if (this.session) {
          this.session.isConnected = true;
          this.session.isStreaming = true;
        }

        if (this.onStatusChange) {
          this.onStatusChange({
            streaming: true,
            message: 'Connected to mediasoup service'
          });
        }
      }
    });

    // Listen for ICE candidates from server
    this.socket.on('webrtc-ice-candidate', async ({ sessionId, candidate }) => {
      if (sessionId === this.session?.sessionId && this.peerConnection && candidate) {
        try {
          await this.peerConnection.addIceCandidate(new RTCIceCandidate(candidate));
        } catch (error) {
          console.error('❌ Error adding ICE candidate:', error);
        }
      }
    });

    // Listen for WebRTC errors
    this.socket.on('webrtc-error', ({ sessionId, error }) => {
      if (sessionId === this.session?.sessionId) {
        console.error(`❌ WebRTC error: ${error}`);
        if (this.onStatusChange) {
          this.onStatusChange({
            streaming: false,
            message: `WebRTC error: ${error}`
          });
        }
      }
    });

    console.log(`🚀 WebRTC streaming initiated!`);
  }

  /**
   * Extract DTLS parameters from SDP offer for mediasoup
   */
  private extractDtlsParameters(offer: RTCSessionDescriptionInit): any {
    if (!offer.sdp) {
      return {
        role: 'client',
        fingerprints: []
      };
    }

    const sdp = offer.sdp;
    const fingerprintMatch = sdp.match(/a=fingerprint:(\S+)\s+(\S+)/);

    return {
      role: 'client',
      fingerprints: fingerprintMatch ? [
        {
          algorithm: fingerprintMatch[1],
          value: fingerprintMatch[2]
        }
      ] : []
    };
  }

  // Format detection no longer needed with WebRTC

  /**
   * Stop streaming
   */
  async stopStreaming(): Promise<void> {
    if (!this.session || !this.socket) {
      return;
    }

    console.log(`🛑 Stopping RTMP stream for session ${this.session.sessionId}`);

    // Close WebRTC peer connection
    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }

    this.socket.emit('stop-stream', {
      sessionId: this.session.sessionId
    });

    // Wait for confirmation
    return new Promise((resolve) => {
      const timeout = setTimeout(resolve, 5000); // 5 second timeout

      this.socket!.on('stream-stopped', ({ sessionId, success, error }) => {
        if (sessionId === this.session!.sessionId) {
          clearTimeout(timeout);
          if (success) {
            console.log(`✅ RTMP stream stopped successfully`);
          } else {
            console.error(`❌ Error stopping RTMP stream: ${error}`);
          }
          this.cleanup();
          resolve();
        }
      });
    });
  }

  /**
   * Disconnect from service
   */
  disconnect(): void {
    console.log('🔌 Disconnecting from RTMP service');
    this.cleanup();
    
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  /**
   * Get current session status
   */
  getStatus(): RTMPSession | null {
    return this.session;
  }

  /**
   * Check if currently streaming
   */
  isStreaming(): boolean {
    return this.session?.isStreaming || false;
  }

  /**
   * Handle disconnection and attempt recovery
   */
  private handleDisconnection(): void {
    console.log('🔄 Attempting to reconnect to RTMP service...');

    if (this.onStatusChange) {
      this.onStatusChange({
        streaming: false,
        message: 'Connection lost, attempting to reconnect...'
      });
    }

    // Try to reconnect after a short delay
    setTimeout(async () => {
      try {
        await this.connect();
        console.log('✅ Reconnected to RTMP service');

        if (this.onStatusChange) {
          this.onStatusChange({
            streaming: true,
            message: 'Reconnected, resuming stream'
          });
        }
      } catch (error) {
        console.error('❌ Failed to reconnect:', error);
        this.cleanup();
      }
    }, 2000);
  }

  /**
   * Cleanup resources
   */
  private cleanup(): void {
    // Close WebRTC peer connection
    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }

    if (this.session) {
      this.session.isConnected = false;
      this.session.isStreaming = false;
    }

    // Notify status change
    if (this.onStatusChange) {
      this.onStatusChange({
        streaming: false,
        message: 'Stream stopped'
      });
    }
  }
}
