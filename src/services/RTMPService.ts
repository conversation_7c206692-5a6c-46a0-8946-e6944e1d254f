import { io, Socket } from 'socket.io-client';

interface RTMPServiceConfig {
  url: string;
  streamKey: string;
  quality?: 'low' | 'medium' | 'high';
  frameRate?: number;
  audioEnabled?: boolean;
}

interface RTMPSession {
  sessionId: string;
  isConnected: boolean;
  isStreaming: boolean;
  tracksAlreadySent: boolean;
  frameCount: number;
  startTime: number;
}

interface StreamQuality {
  width: number;
  height: number;
  frameRate: number;
  videoBitrate: string;
  audioBitrate: string;
}

export class RTMPService {
  private socket: Socket | null = null;
  private session: RTMPSession | null = null;
  private serviceUrl: string;
  private peerConnection: RTCPeerConnection | null = null;
  private onStatusChange?: (status: { streaming: boolean; message: string }) => void;

  // Frame capture properties
  private canvas: HTMLCanvasElement | null = null;
  private ctx: CanvasRenderingContext2D | null = null;
  private frameInterval: number | null = null;
  private localVideo: HTMLVideoElement | null = null;

  // Audio capture properties
  private audioContext: AudioContext | null = null;
  private audioProcessor: ScriptProcessorNode | null = null;
  private localStream: MediaStream | null = null;

  // Performance optimization properties
  private frameBuffer: string[] = [];
  private lastFrameTime: number = 0;
  private frameSkipCount: number = 0;

  // Production features
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 5;
  private reconnectDelay: number = 2000;
  private healthCheckInterval: number | null = null;
  private connectionTimeout: number | null = null;

  // Quality monitoring
  private qualityMonitorInterval: number | null = null;
  private framesSentLastSecond: number = 0;
  private targetFPS: number = 24;
  private autoQualityEnabled: boolean = true;
  private currentQuality: StreamQuality = {
    width: 1920,
    height: 1080,
    frameRate: 24, // Reduced from 30 for better performance
    videoBitrate: '2500k',
    audioBitrate: '128k'
  };

  constructor(serviceUrl: string) {
    this.serviceUrl = serviceUrl;
    const instanceId = Math.random().toString(36).substring(2, 8);
    console.log(`🆔 RTMPService instance created: ${instanceId}`);
    this.setupCanvas();
  }

  /**
   * Set up canvas for frame capture
   */
  private setupCanvas(): void {
    this.canvas = document.createElement('canvas');
    this.ctx = this.canvas.getContext('2d');
    this.canvas.style.display = 'none';
    document.body.appendChild(this.canvas);
  }

  /**
   * Configure stream quality
   */
  setQuality(quality: 'low' | 'medium' | 'high'): void {
    const qualitySettings: Record<string, StreamQuality> = {
      low: {
        width: 1280,
        height: 720,
        frameRate: 15, // Low bandwidth
        videoBitrate: '1000k',
        audioBitrate: '96k'
      },
      medium: {
        width: 1920,
        height: 1080,
        frameRate: 20, // Balanced performance
        videoBitrate: '2000k', // Reduced for better stability
        audioBitrate: '128k'
      },
      high: {
        width: 1920,
        height: 1080,
        frameRate: 24, // Reduced from 30 for stability
        videoBitrate: '3500k', // Reduced from 4000k
        audioBitrate: '160k'
      }
    };

    this.currentQuality = qualitySettings[quality];
    console.log(`📐 Stream quality set to ${quality}:`, this.currentQuality);
    this.targetFPS = this.currentQuality.frameRate;
  }

  /**
   * Enable or disable automatic quality adjustment
   */
  setAutoQuality(enabled: boolean): void {
    this.autoQualityEnabled = enabled;
    console.log(`🎛️ Auto quality adjustment: ${enabled ? 'enabled' : 'disabled'}`);

    if (enabled) {
      this.startQualityMonitoring();
    } else {
      this.stopQualityMonitoring();
    }
  }

  /**
   * Get current streaming statistics
   */
  getStreamingStats(): any {
    return {
      frameCount: this.session?.frameCount || 0,
      framesSentLastSecond: this.framesSentLastSecond,
      targetFPS: this.targetFPS,
      currentQuality: this.currentQuality,
      frameSkipCount: this.frameSkipCount,
      bufferSize: this.frameBuffer.length,
      isConnected: this.socket?.connected || false,
      isStreaming: this.session?.isStreaming || false
    };
  }

  /**
   * Set up canvas dimensions for video capture
   */
  private setupCanvasForVideo(): void {
    if (!this.canvas || !this.localVideo) return;

    this.canvas.width = this.currentQuality.width;
    this.canvas.height = this.currentQuality.height;
    console.log(`📐 Canvas configured: ${this.canvas.width}x${this.canvas.height}`);
  }

  /**
   * Set callback for status changes
   */
  setStatusCallback(callback: (status: { streaming: boolean; message: string }) => void) {
    this.onStatusChange = callback;
  }

  /**
   * Disconnect from the RTMP service
   */
  async disconnect(): Promise<void> {
    console.log('🔌 Disconnecting from RTMP service');

    // Stop streaming if active
    if (this.session) {
      await this.stopStreaming();
    }

    // Disconnect socket
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }

    // Clean up canvas
    if (this.canvas && this.canvas.parentNode) {
      this.canvas.parentNode.removeChild(this.canvas);
    }

    // Clean up health check
    this.stopHealthCheck();
  }

  /**
   * Handle connection errors with retry logic
   */
  private handleConnectionError(error: any): void {
    this.reconnectAttempts++;
    console.error(`❌ Connection error (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts}):`, error);

    if (this.onStatusChange) {
      this.onStatusChange({
        streaming: false,
        message: `Connection error - retrying (${this.reconnectAttempts}/${this.maxReconnectAttempts})`
      });
    }
  }

  /**
   * Handle disconnection events
   */
  private handleDisconnection(reason: string): void {
    console.warn(`🔌 Handling disconnection: ${reason}`);

    // Stop streaming if active
    if (this.session?.isStreaming) {
      this.stopStreaming().catch(console.error);
    }

    if (this.onStatusChange) {
      this.onStatusChange({
        streaming: false,
        message: reason === 'io server disconnect' ? 'Server disconnected' : 'Connection lost - reconnecting...'
      });
    }
  }

  /**
   * Start health check monitoring
   */
  private startHealthCheck(): void {
    this.stopHealthCheck(); // Clear any existing interval

    this.healthCheckInterval = window.setInterval(() => {
      if (this.socket?.connected) {
        this.socket.emit('ping', { timestamp: Date.now() });
      }
    }, 30000); // Ping every 30 seconds

    console.log('💓 Health check started');
  }

  /**
   * Stop health check monitoring
   */
  private stopHealthCheck(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
      console.log('💓 Health check stopped');
    }
  }

  /**
   * Start quality monitoring and auto-adjustment
   */
  private startQualityMonitoring(): void {
    this.stopQualityMonitoring(); // Clear any existing interval

    this.qualityMonitorInterval = window.setInterval(() => {
      if (!this.session?.isStreaming) return;

      const actualFPS = this.framesSentLastSecond;
      const targetFPS = this.targetFPS;
      const fpsRatio = actualFPS / targetFPS;

      console.log(`📊 Quality monitor: ${actualFPS}/${targetFPS} fps (${(fpsRatio * 100).toFixed(1)}%)`);

      // Auto-adjust quality based on performance
      if (this.autoQualityEnabled) {
        if (fpsRatio < 0.7 && this.currentQuality.frameRate > 15) {
          // Performance is poor, reduce quality
          console.log(`📉 Reducing quality due to poor performance`);
          this.autoAdjustQuality('down');
        } else if (fpsRatio > 0.95 && this.frameSkipCount < 2 && this.currentQuality.frameRate < 30) {
          // Performance is good, try increasing quality
          console.log(`📈 Increasing quality due to good performance`);
          this.autoAdjustQuality('up');
        }
      }

      // Reset counter
      this.framesSentLastSecond = 0;

    }, 5000); // Check every 5 seconds

    console.log('📊 Quality monitoring started');
  }

  /**
   * Stop quality monitoring
   */
  private stopQualityMonitoring(): void {
    if (this.qualityMonitorInterval) {
      clearInterval(this.qualityMonitorInterval);
      this.qualityMonitorInterval = null;
      console.log('📊 Quality monitoring stopped');
    }
  }

  /**
   * Auto-adjust quality based on performance
   */
  private autoAdjustQuality(direction: 'up' | 'down'): void {
    const currentFPS = this.currentQuality.frameRate;

    if (direction === 'down') {
      if (currentFPS > 20) {
        this.currentQuality.frameRate = 20;
        this.currentQuality.videoBitrate = '1800k';
      } else if (currentFPS > 15) {
        this.currentQuality.frameRate = 15;
        this.currentQuality.videoBitrate = '1200k';
      }
    } else {
      if (currentFPS < 20) {
        this.currentQuality.frameRate = 20;
        this.currentQuality.videoBitrate = '2000k';
      } else if (currentFPS < 24) {
        this.currentQuality.frameRate = 24;
        this.currentQuality.videoBitrate = '2500k';
      }
    }

    this.targetFPS = this.currentQuality.frameRate;
    console.log(`🎛️ Auto-adjusted quality:`, this.currentQuality);

    if (this.onStatusChange) {
      this.onStatusChange({
        streaming: true,
        message: `Quality auto-adjusted to ${this.currentQuality.frameRate}fps`
      });
    }
  }

  /**
   * Connect to the RTMP service
   */
  async connect(): Promise<void> {
    if (this.socket?.connected) {
      console.log('🔌 Already connected to RTMP service');
      return;
    }

    console.log(`🔌 Connecting to RTMP service at ${this.serviceUrl}`);

    this.socket = io(this.serviceUrl, {
      transports: ['websocket', 'polling'],
      timeout: 10000,
      reconnection: true,
      reconnectionAttempts: this.maxReconnectAttempts,
      reconnectionDelay: this.reconnectDelay
    });

    return new Promise((resolve, reject) => {
      this.connectionTimeout = window.setTimeout(() => {
        reject(new Error('RTMP service connection timeout'));
      }, 10000);

      this.socket!.on('connect', () => {
        if (this.connectionTimeout) {
          clearTimeout(this.connectionTimeout);
          this.connectionTimeout = null;
        }

        console.log('✅ Connected to RTMP service');
        console.log(`🆔 Socket ID: ${this.socket!.id}`);
        this.reconnectAttempts = 0; // Reset on successful connection
        this.startHealthCheck();
        resolve();
      });

      this.socket!.on('connect_error', (error) => {
        if (this.connectionTimeout) {
          clearTimeout(this.connectionTimeout);
          this.connectionTimeout = null;
        }

        console.error('❌ RTMP service connection error:', error);
        this.handleConnectionError(error);
        reject(new Error(`RTMP service connection failed: ${error.message}`));
      });

      this.socket!.on('disconnect', (reason) => {
        console.warn(`🔌 Disconnected from RTMP service: ${reason}`);
        this.stopHealthCheck();
        this.handleDisconnection(reason);
      });

      this.socket!.on('reconnect', (attemptNumber) => {
        console.log(`🔄 Reconnected to RTMP service (attempt ${attemptNumber})`);
        this.reconnectAttempts = 0;
        this.startHealthCheck();
      });

      this.socket!.on('reconnect_error', (error) => {
        console.error(`❌ Reconnection failed:`, error);
        this.handleConnectionError(error);
      });

      this.socket!.on('reconnect_failed', () => {
        console.error(`❌ All reconnection attempts failed`);
        if (this.onStatusChange) {
          this.onStatusChange({
            streaming: false,
            message: 'Connection failed - please refresh page'
          });
        }
      });

      // Add debugging for all events
      this.socket!.onAny((eventName, ...args) => {
        console.log(`📡 Socket event: ${eventName}`, args);
      });
    });
  }

  /**
   * Start streaming to RTMP endpoint with real video
   */
  async startStreaming(config: RTMPServiceConfig, videoElement?: HTMLVideoElement): Promise<void> {
    if (!this.socket?.connected) {
      throw new Error('Not connected to RTMP service');
    }

    // Apply quality settings if provided
    if (config.quality) {
      this.setQuality(config.quality);
    }
    if (config.frameRate) {
      this.currentQuality.frameRate = config.frameRate;
    }

    console.log(`🚀 Starting RTMP stream to ${config.url} with quality:`, this.currentQuality);

    if (videoElement) {
      this.localVideo = videoElement;

      // Create canvas for frame capture from video element
      this.canvas = document.createElement('canvas');
      this.ctx = this.canvas.getContext('2d');

      this.setupCanvasForVideo();
      console.log(`📹 Canvas created for video element capture`);
    }

    // Request stream session using our working approach
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Stream session creation timeout'));
      }, 15000);

      // Use the working direct WebRTC approach
      this.socket!.emit('start-stream', {
        rtmpUrl: config.url,
        streamKey: config.streamKey,
        quality: this.currentQuality,
        audioEnabled: config.audioEnabled !== false
      });

      this.socket!.once('stream-started', async ({ sessionId, success, error }) => {
        clearTimeout(timeout);

        if (success) {
          this.session = {
            sessionId,
            isConnected: false,
            isStreaming: false,
            tracksAlreadySent: false,
            frameCount: 0,
            startTime: Date.now()
          };
          console.log(`✅ Stream session created: ${sessionId}`);

          try {
            // Set up WebRTC connection
            await this.setupWebRTCConnection();
            resolve();
          } catch (webrtcError) {
            console.error(`❌ WebRTC setup failed:`, webrtcError);
            reject(webrtcError);
          }
        } else {
          console.error(`❌ Failed to create stream session: ${error}`);
          reject(new Error(`Failed to create stream session: ${error}`));
        }
      });
    });
  }

  /**
   * Setup WebRTC connection for real video streaming
   */
  private async setupWebRTCConnection(): Promise<void> {
    if (!this.session) {
      throw new Error('No active session');
    }

    console.log(`🔗 Setting up WebRTC connection for session ${this.session.sessionId}`);

    // Create peer connection
    this.peerConnection = new RTCPeerConnection({
      iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
    });

    // Handle ICE candidates
    this.peerConnection.onicecandidate = (event) => {
      if (event.candidate && this.socket && this.session) {
        this.socket.emit('webrtc-ice-candidate', {
          sessionId: this.session.sessionId,
          candidate: event.candidate
        });
      }
    };

    // Add video track from local video element
    console.log(`🔍 Checking video element: ${!!this.localVideo}`);
    console.log(`🔍 Video srcObject: ${!!this.localVideo?.srcObject}`);

    if (this.localVideo && this.localVideo.srcObject) {
      const stream = this.localVideo.srcObject as MediaStream;
      this.localStream = stream;

      console.log(`🎬 Adding tracks from stream with ${stream.getTracks().length} tracks`);

      stream.getTracks().forEach(track => {
        if (this.peerConnection) {
          this.peerConnection.addTrack(track, stream);
          console.log(`✅ Added ${track.kind} track to peer connection`);

          // Set up audio capture for audio tracks
          if (track.kind === 'audio') {
            this.setupAudioCapture(stream);
          }
        }
      });
    } else {
      console.warn(`⚠️ No video element or srcObject available for WebRTC`);
    }

    console.log(`🎯 Continuing to connection state handler setup...`);

    // Set up connection state change handler
    this.peerConnection.onconnectionstatechange = () => {
      if (this.peerConnection) {
        console.log(`🔗 Connection state: ${this.peerConnection.connectionState}`);
        if (this.peerConnection.connectionState === 'connected') {
          console.log(`✅ WebRTC connection established`);
        } else if (this.peerConnection.connectionState === 'failed' ||
                   this.peerConnection.connectionState === 'disconnected') {
          console.log(`❌ WebRTC connection failed/disconnected`);
          this.handleDisconnection('webrtc connection failed');
        }
      }
    };

    // Set up WebRTC answer listener BEFORE sending offer
    console.log(`🎧 Setting up webrtc-answer listener for session ${this.session.sessionId}`);
    this.socket.on('webrtc-answer', async ({ sessionId, answer }) => {
      console.log(`📨 [HANDLER] WebRTC answer received for session ${sessionId}`);
      console.log(`🔍 Current session: ${this.session?.sessionId}`);
      console.log(`🔍 Peer connection exists: ${!!this.peerConnection}`);

      if (sessionId === this.session?.sessionId && this.peerConnection) {
        console.log(`✅ Session and peer connection match - processing answer`);

        try {
          await this.peerConnection.setRemoteDescription(answer);
          console.log(`✅ WebRTC connection established!`);
          console.log(`🎬 Starting real video frame capture...`);

          // Start capturing and sending real video frames
          await this.startFrameCapture();

          // Start quality monitoring
          if (this.autoQualityEnabled) {
            this.startQualityMonitoring();
          }

          if (this.session) {
            this.session.isConnected = true;
            this.session.isStreaming = true;
          }

          if (this.onStatusChange) {
            this.onStatusChange({
              streaming: true,
              message: 'Streaming real video to RTMP endpoint'
            });
          }
        } catch (error) {
          console.error(`❌ Failed to set remote description:`, error);
        }
      }
    });

    // Listen for ICE candidates from server
    this.socket.on('webrtc-ice-candidate', async ({ sessionId, candidate }) => {
      if (sessionId === this.session?.sessionId && this.peerConnection && candidate) {
        try {
          await this.peerConnection.addIceCandidate(new RTCIceCandidate(candidate));
        } catch (error) {
          console.error('❌ Error adding ICE candidate:', error);
        }
      }
    });

    // Now create and send offer (after listeners are set up)
    const offer = await this.peerConnection.createOffer();
    await this.peerConnection.setLocalDescription(offer);

    console.log(`📤 Sending WebRTC offer`);
    console.log(`📋 Offer SDP type: ${offer.type}`);

    // Send the actual WebRTC offer (not custom DTLS format)
    this.socket.emit('webrtc-offer', {
      sessionId: this.session.sessionId,
      offer: offer  // Send the actual SDP offer
    });

    console.log(`🚀 WebRTC streaming initiated!`);
  }

  /**
   * Start capturing video frames and sending to server
   */
  private async startFrameCapture(): Promise<void> {
    console.log(`🔍 Checking frame capture components:`);
    console.log(`  Canvas: ${!!this.canvas}`);
    console.log(`  Context: ${!!this.ctx}`);
    console.log(`  Video: ${!!this.localVideo}`);
    console.log(`  Session: ${!!this.session}`);

    if (!this.canvas || !this.ctx || !this.localVideo || !this.session) {
      console.error(`❌ Cannot start frame capture - missing components`);
      return;
    }

    console.log(`📹 Starting real video frame capture at ${this.currentQuality.frameRate} FPS...`);
    console.log(`📐 Video element dimensions: ${this.localVideo.videoWidth}x${this.localVideo.videoHeight}`);
    console.log(`📐 Canvas dimensions: ${this.canvas.width}x${this.canvas.height}`);

    // Ensure video is playing
    if (this.localVideo.paused) {
      console.log(`▶️ Video is paused, starting playback...`);
      try {
        await this.localVideo.play();
        console.log(`✅ Video playback started`);
      } catch (error) {
        console.error(`❌ Failed to start video playback:`, error);
      }
    }

    this.frameInterval = window.setInterval(() => {
      try {
        if (!this.canvas || !this.ctx || !this.localVideo || !this.session) {
          console.log(`⚠️ Frame capture skipped - missing components`);
          return;
        }

        // Debug: Log first few frame attempts
        if (this.session.frameCount < 3) {
          console.log(`🎬 Frame capture attempt ${this.session.frameCount + 1}`);
          console.log(`📺 Video playing: ${!this.localVideo.paused}`);
          console.log(`📺 Video time: ${this.localVideo.currentTime}`);
          console.log(`📺 Video ready state: ${this.localVideo.readyState}`);
        }

        const now = Date.now();
        const timeSinceLastFrame = now - this.lastFrameTime;
        const targetFrameTime = 1000 / this.currentQuality.frameRate;

        // Skip frame if we're running too fast (adaptive frame rate)
        if (timeSinceLastFrame < targetFrameTime * 0.9) {
          this.frameSkipCount++;
          return;
        }

        this.lastFrameTime = now;

        // Draw current video frame to canvas
        this.ctx.drawImage(this.localVideo, 0, 0, this.canvas.width, this.canvas.height);

        // Adaptive JPEG quality based on frame rate performance
        const quality = this.frameSkipCount > 5 ? 0.6 : 0.75; // Lower quality if skipping frames
        const frameData = this.canvas.toDataURL('image/jpeg', quality);

        // Buffer frames to smooth out transmission
        this.frameBuffer.push(frameData);

        // Send buffered frames (max 3 frames in buffer)
        if (this.frameBuffer.length >= 3) {
          const bufferedFrame = this.frameBuffer.shift()!;

          this.socket!.emit('video-frame', {
            sessionId: this.session.sessionId,
            frameData: bufferedFrame,
            width: this.canvas.width,
            height: this.canvas.height,
            timestamp: now
          });

          this.framesSentLastSecond++; // Track for quality monitoring
        }

        this.session.frameCount++;

        // Reset skip count periodically
        if (this.session.frameCount % 30 === 0) {
          console.log(`📹 Captured ${this.session.frameCount} frames (skipped: ${this.frameSkipCount}, quality: ${quality})`);
          this.frameSkipCount = 0;
        }

      } catch (error) {
        console.error('Frame capture error:', error);
      }
    }, 1000 / this.currentQuality.frameRate);
  }

  /**
   * Setup audio capture from MediaStream
   */
  private setupAudioCapture(stream: MediaStream): void {
    try {
      console.log(`🎵 Setting up audio capture...`);

      // Create audio context
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();

      // Create media stream source
      const source = this.audioContext.createMediaStreamSource(stream);

      // Create script processor for audio data (larger buffer for efficiency)
      this.audioProcessor = this.audioContext.createScriptProcessor(8192, 1, 1);

      let audioChunkCount = 0;

      this.audioProcessor.onaudioprocess = (event) => {
        if (!this.session) return;

        // Send audio less frequently to reduce bandwidth
        audioChunkCount++;
        if (audioChunkCount % 3 !== 0) return; // Send every 3rd chunk

        const inputBuffer = event.inputBuffer;
        const inputData = inputBuffer.getChannelData(0);

        // Downsample audio for efficiency (take every 4th sample)
        const downsampledData = new Float32Array(inputData.length / 4);
        for (let i = 0; i < downsampledData.length; i++) {
          downsampledData[i] = inputData[i * 4];
        }

        // Convert to base64 for transmission
        const audioData = btoa(String.fromCharCode(...new Uint8Array(downsampledData.buffer)));

        // Send audio chunk to server
        this.socket!.emit('audio-chunk', {
          sessionId: this.session.sessionId,
          audioData: audioData,
          timestamp: Date.now()
        });
      };

      // Connect audio nodes
      source.connect(this.audioProcessor);
      this.audioProcessor.connect(this.audioContext.destination);

      console.log(`✅ Audio capture setup complete`);

    } catch (error) {
      console.error(`❌ Audio capture setup failed:`, error);
    }
  }

  /**
   * Setup video streaming with canvas (backward compatibility)
   */
  async setupVideoStreaming(canvas: HTMLCanvasElement): Promise<void> {
    if (!this.session) {
      throw new Error('No active session. Call startStreaming first.');
    }

    console.log(`🎥 Setting up video streaming for session ${this.session.sessionId}`);
    console.log(`📐 Canvas dimensions: ${canvas.width}x${canvas.height}`);

    // Verify canvas is ready
    if (canvas.width === 0 || canvas.height === 0) {
      console.error(`❌ Canvas has invalid dimensions: ${canvas.width}x${canvas.height}`);
      throw new Error('Canvas has invalid dimensions');
    }

    // Check if canvas has any content
    const ctx = canvas.getContext('2d');
    if (ctx) {
      const imageData = ctx.getImageData(0, 0, Math.min(canvas.width, 10), Math.min(canvas.height, 10));
      const hasContent = imageData.data.some(pixel => pixel !== 0);
      console.log(`🖼️ Composite canvas has content: ${hasContent}`);

      if (!hasContent) {
        console.warn(`⚠️ Composite canvas appears to be empty - WebGL may not be rendering yet`);
        console.log(`🔍 Canvas context type: ${canvas.getContext ? 'available' : 'not available'}`);
        console.log(`🔍 Canvas parent: ${canvas.parentElement?.tagName || 'none'}`);
      } else {
        console.log(`✅ Composite canvas is rendering content - ready for streaming`);
      }
    }

    // Use the provided canvas instead of creating our own
    this.canvas = canvas;
    this.ctx = canvas.getContext('2d');

    // Update quality based on canvas size
    this.currentQuality.width = canvas.width;
    this.currentQuality.height = canvas.height;

    // Start capturing frames from canvas
    console.log(`📹 Using canvas-based frame capture`);
    this.startCanvasFrameCapture();
  }

  /**
   * Start capturing frames directly from canvas
   */
  private startCanvasFrameCapture(): void {
    if (!this.canvas || !this.session) {
      console.error(`❌ Cannot start canvas frame capture - missing components`);
      return;
    }

    console.log(`📹 Starting canvas frame capture at ${this.currentQuality.frameRate} FPS...`);

    this.frameInterval = window.setInterval(() => {
      try {
        if (!this.canvas || !this.session) return;

        const now = Date.now();
        const timeSinceLastFrame = now - this.lastFrameTime;
        const targetFrameTime = 1000 / this.currentQuality.frameRate;

        // Skip frame if we're running too fast
        if (timeSinceLastFrame < targetFrameTime * 0.9) {
          this.frameSkipCount++;
          return;
        }

        this.lastFrameTime = now;

        // Adaptive quality based on performance
        const quality = this.frameSkipCount > 5 ? 0.6 : 0.75;

        // For WebGL canvas, we need to capture directly
        let frameData: string;
        if (this.canvas.getContext('webgl') || this.canvas.getContext('webgl2')) {
          // WebGL canvas - capture directly
          frameData = this.canvas.toDataURL('image/jpeg', quality);
        } else {
          // Regular 2D canvas - capture directly
          frameData = this.canvas.toDataURL('image/jpeg', quality);
        }

        // Debug: Check frame data size and uniqueness
        if (this.session.frameCount === 0) {
          console.log(`🖼️ First frame captured, size: ${frameData.length} chars`);
          console.log(`🖼️ Frame data preview: ${frameData.substring(0, 100)}...`);
        }

        // Check if frames are changing (every 30 frames)
        if (this.session.frameCount % 30 === 0) {
          const frameHash = frameData.substring(50, 100); // Simple hash
          console.log(`🔄 Frame ${this.session.frameCount} hash: ${frameHash}`);

          // Check if canvas is actually rendering content
          if (this.canvas.getContext) {
            const ctx = this.canvas.getContext('2d');
            if (ctx) {
              const imageData = ctx.getImageData(0, 0, 1, 1);
              const pixel = imageData.data;
              console.log(`🎨 Canvas pixel sample: R:${pixel[0]} G:${pixel[1]} B:${pixel[2]} A:${pixel[3]}`);
            }
          }
        }

        // Buffer frames for smooth transmission
        this.frameBuffer.push(frameData);

        if (this.frameBuffer.length >= 2) { // Smaller buffer for canvas
          const bufferedFrame = this.frameBuffer.shift()!;

          // Debug: Log frame transmission
          if (this.session.frameCount % 60 === 0) { // Every 2 seconds at 30fps
            console.log(`📤 Sending frame ${this.session.frameCount} to server (${bufferedFrame.length} chars)`);
          }

          this.socket!.emit('video-frame', {
            sessionId: this.session.sessionId,
            frameData: bufferedFrame,
            width: this.canvas.width,
            height: this.canvas.height,
            timestamp: now
          });
        }

        this.session.frameCount++;

        // Log progress and reset skip count
        if (this.session.frameCount % 30 === 0) {
          console.log(`📹 Canvas: ${this.session.frameCount} frames (skipped: ${this.frameSkipCount}, quality: ${quality})`);
          this.frameSkipCount = 0;
        }

      } catch (error) {
        console.error('Canvas frame capture error:', error);
      }
    }, 1000 / this.currentQuality.frameRate);
  }

  /**
   * Start WebRTC streaming
   */
  private async startWebRTCStreaming(stream: MediaStream): Promise<void> {
    if (!this.socket || !this.session) {
      console.error('❌ Cannot start WebRTC: missing socket or session');
      return;
    }

    console.log(`🎬 Starting WebRTC streaming`);
    console.log(`🔌 Socket connected: ${this.socket.connected}`);
    console.log(`📺 Session ID: ${this.session.sessionId}`);

    // Create RTCPeerConnection
    this.peerConnection = new RTCPeerConnection({
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' }
      ]
    });

    // Add stream tracks to peer connection
    stream.getTracks().forEach(track => {
      if (this.peerConnection) {
        this.peerConnection.addTrack(track, stream);
        console.log(`➕ Added ${track.kind} track to peer connection`);
      }
    });

    // Handle ICE candidates
    this.peerConnection.onicecandidate = (event) => {
      if (event.candidate && this.socket && this.session) {
        this.socket.emit('webrtc-ice-candidate', {
          sessionId: this.session.sessionId,
          candidate: event.candidate
        });
      }
    };

    // Handle connection state changes
    this.peerConnection.onconnectionstatechange = () => {
      if (this.peerConnection) {
        console.log(`🔄 WebRTC connection state: ${this.peerConnection.connectionState}`);

        if (this.peerConnection.connectionState === 'connected') {
          console.log(`✅ WebRTC connection established`);
        } else if (this.peerConnection.connectionState === 'failed' ||
                   this.peerConnection.connectionState === 'disconnected') {
          console.log(`❌ WebRTC connection failed/disconnected`);
          this.handleDisconnection('webrtc connection failed');
        }
      }
    };
  }

  /**
   * Extract DTLS parameters from SDP offer for mediasoup
   */
  private extractDtlsParameters(offer: RTCSessionDescriptionInit): any {
    if (!offer.sdp) {
      return {
        role: 'client',
        fingerprints: []
      };
    }

    const sdp = offer.sdp;
    const fingerprintMatch = sdp.match(/a=fingerprint:(\S+)\s+(\S+)/);

    return {
      role: 'client',
      fingerprints: fingerprintMatch ? [
        {
          algorithm: fingerprintMatch[1],
          value: fingerprintMatch[2]
        }
      ] : []
    };
  }

  /**
   * Send video tracks to mediasoup service
   */
  private async sendVideoTracksToMediasoup(transportParams: any): Promise<void> {
    try {
      console.log('🎥 Sending video tracks to mediasoup...');

      // Get video track from peer connection
      const senders = this.peerConnection?.getSenders() || [];
      const videoSender = senders.find(sender =>
        sender.track && sender.track.kind === 'video'
      );

      if (!videoSender || !videoSender.track) {
        console.error('❌ No video track found to send to mediasoup');
        return;
      }

      const videoTrack = videoSender.track;
      console.log(`📹 Found video track: ${videoTrack.id}, enabled: ${videoTrack.enabled}`);

      // For mediasoup, we need to create a producer
      // This is a simplified approach - in a full implementation,
      // you'd use the mediasoup client library

      // Send produce request to mediasoup
      this.socket.emit('produce', {
        sessionId: this.session?.sessionId,
        kind: 'video',
        rtpParameters: await this.getRtpParameters(videoTrack),
        transportId: transportParams.id
      });

      console.log('✅ Video track sent to mediasoup for processing');

    } catch (error) {
      console.error('❌ Error sending video tracks to mediasoup:', error);
    }
  }

  /**
   * Get RTP parameters for a media track
   */
  private async getRtpParameters(track: MediaStreamTrack): Promise<any> {
    // This is a simplified version - in a real implementation,
    // you'd extract proper RTP parameters from the track
    return {
      codecs: [
        {
          mimeType: 'video/VP8',
          clockRate: 90000,
          payloadType: 96
        }
      ],
      headerExtensions: [],
      encodings: [
        {
          ssrc: Math.floor(Math.random() * 1000000000)
        }
      ],
      rtcp: {
        cname: `track-${track.id}`
      }
    };
  }

  // Format detection no longer needed with WebRTC

  /**
   * Stop streaming and clean up resources
   */
  async stopStreaming(): Promise<void> {
    if (!this.session || !this.socket) {
      return;
    }

    console.log(`🛑 Stopping stream for session ${this.session.sessionId}`);

    // Stop frame capture
    if (this.frameInterval) {
      clearInterval(this.frameInterval);
      this.frameInterval = null;
      console.log(`🛑 Stopped frame capture`);
    }

    // Stop quality monitoring
    this.stopQualityMonitoring();

    // Stop audio capture
    if (this.audioProcessor) {
      this.audioProcessor.disconnect();
      this.audioProcessor = null;
      console.log(`🛑 Stopped audio capture`);
    }

    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }

    // Close WebRTC peer connection
    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }

    this.socket.emit('stop-stream', {
      sessionId: this.session.sessionId
    });

    // Wait for confirmation
    return new Promise((resolve) => {
      const timeout = setTimeout(resolve, 5000); // 5 second timeout

      this.socket!.on('stream-stopped', ({ sessionId, success, error }) => {
        if (sessionId === this.session!.sessionId) {
          clearTimeout(timeout);
          if (success) {
            console.log(`✅ RTMP stream stopped successfully`);
          } else {
            console.error(`❌ Error stopping RTMP stream: ${error}`);
          }
          this.cleanup();
          resolve();
        }
      });
    });
  }



  /**
   * Get current session status
   */
  getStatus(): RTMPSession | null {
    return this.session;
  }

  /**
   * Check if currently streaming
   */
  isStreaming(): boolean {
    return this.session?.isStreaming || false;
  }



  /**
   * Cleanup resources
   */
  private cleanup(): void {
    // Close WebRTC peer connection
    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }

    if (this.session) {
      this.session.isConnected = false;
      this.session.isStreaming = false;
    }

    // Notify status change
    if (this.onStatusChange) {
      this.onStatusChange({
        streaming: false,
        message: 'Stream stopped'
      });
    }
  }
}
