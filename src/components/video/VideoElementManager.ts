/**
 * Video element management for WebRTC streams
 */

export class VideoElementManager {
  private videoRefs: Record<string, HTMLVideoElement> = {};
  private localVideoRef: HTMLVideoElement | null = null;
  private overlayVideoRef: HTMLVideoElement | null = null;
  private logoImageRef: HTMLImageElement | null = null;

  constructor() {
  }

  /**
   * Create or get local video element
   */
  getOrCreateLocalVideo(stream: MediaStream): HTMLVideoElement {
    if (!this.localVideoRef) {
      const video = document.createElement('video');
      video.autoplay = true;
      video.playsInline = true;
      video.muted = true;
      video.style.position = 'absolute';
      video.style.top = '0px';
      video.style.left = '0px';
      video.style.width = '1px';
      video.style.height = '1px';
      video.style.opacity = '0.01'; // Nearly invisible but still rendered
      video.style.pointerEvents = 'none';
      video.style.zIndex = '-1000';

      // Add to DOM to ensure proper loading
      document.body.appendChild(video);

      this.localVideoRef = video;
    }

    if (this.localVideoRef.srcObject !== stream) {
      const video = this.localVideoRef;
      video.srcObject = stream;

      // Force video to load and play
      video.load();
      video.play().then(() => {
        console.log(`✅ Local video playing: ${!video.paused}, time: ${video.currentTime}, ready: ${video.readyState}`);
      }).catch((error) => {
        console.error('❌ Failed to play local video:', error);
      });
    }

    return this.localVideoRef;
  }

  /**
   * Remove local video element
   */
  removeLocalVideo(): void {
    if (this.localVideoRef) {
      if (this.localVideoRef.parentNode) {
        this.localVideoRef.parentNode.removeChild(this.localVideoRef);
      }
      this.localVideoRef.srcObject = null;
      this.localVideoRef = null;
    }
  }

  /**
   * Get or create peer video element
   */
  getOrCreatePeerVideo(peerId: string, peer: any): HTMLVideoElement {
    if (!this.videoRefs[peerId]) {
      const video = document.createElement('video');
      video.autoplay = true;
      video.playsInline = true;
      video.muted = true;
      video.style.position = 'absolute';
      video.style.top = '0px';
      video.style.left = '0px';
      video.style.width = '1px';
      video.style.height = '1px';
      video.style.opacity = '0.01';
      video.style.pointerEvents = 'none';
      video.style.zIndex = '-1000';

      // Add to DOM to ensure proper loading
      document.body.appendChild(video);

      this.videoRefs[peerId] = video;
    }

    // Update stream if it exists and is different
    if (peer.stream && this.videoRefs[peerId].srcObject !== peer.stream) {
      this.assignStreamToPeerVideo(peerId, peer);
    }

    return this.videoRefs[peerId];
  }

  /**
   * Assign stream to peer video with comprehensive error handling
   */
  private assignStreamToPeerVideo(peerId: string, peer: any): void {
    const video = this.videoRefs[peerId];
    
    // Validate stream has video tracks
    const videoTracks = peer.stream.getVideoTracks();
    if (videoTracks.length === 0) {
      return;
    }

    video.srcObject = peer.stream;

    // Add comprehensive event listeners to track video loading
    this.addVideoEventListeners(video);

    // Force video to load and play with multiple attempts
    this.attemptVideoPlay(video, peerId, peer);
  }

  /**
   * Add event listeners to video element
   */
  private addVideoEventListeners(video: HTMLVideoElement): void {
    // Add minimal event listeners without logging
    video.addEventListener('error', () => {
      // Handle error silently
    });
  }

  /**
   * Attempt to play video with recovery mechanisms
   */
  private async attemptVideoPlay(video: HTMLVideoElement, peerId: string, peer: any): Promise<void> {
    try {
      // Ensure video properties are set correctly
      video.autoplay = true;
      video.playsInline = true;
      video.muted = true;

      // Try different approaches to get the video to play
      video.load();

      // Wait a bit for the stream to be ready
      await new Promise(resolve => setTimeout(resolve, 100));

      await video.play();
    } catch (error) {
      console.error(`❌ Failed to play video for peer ${peerId}:`, error);
      this.setupVideoRecovery(video, peerId, peer);
    }
  }

  /**
   * Set up video recovery mechanisms
   */
  private setupVideoRecovery(video: HTMLVideoElement, peerId: string, peer: any): void {
    // Set up periodic checks to monitor video loading with escalating recovery
    let checkCount = 0;
    const checkInterval = setInterval(() => {
      checkCount++;

      if (video.readyState === 0 && video.srcObject) {
        // Escalating recovery attempts
        if (checkCount === 3) {
          // After 6 seconds, try resetting srcObject
          const currentStream = video.srcObject;
          video.srcObject = null;
          setTimeout(() => {
            video.srcObject = currentStream;
            video.load();
            video.play().catch((error) => {
              console.warn(`⚠️ Reset play failed for peer ${peerId}:`, error);
            });
          }, 200);
        } else if (checkCount === 6) {
          // After 12 seconds, try the nuclear option - complete recreation
          this.recreateVideoElement(peerId, peer);
        }
      } else if (video.readyState >= 2) {
        // Video is loading/loaded, clear the interval
        clearInterval(checkInterval);
      }
    }, 2000);

    // Clear interval after 30 seconds to avoid memory leaks
    setTimeout(() => {
      clearInterval(checkInterval);
    }, 30000);
  }

  /**
   * Recreate video element as last resort
   */
  private recreateVideoElement(peerId: string, peer: any): void {
    const oldVideo = this.videoRefs[peerId];
    if (oldVideo && oldVideo.parentNode) {
      oldVideo.parentNode.removeChild(oldVideo);
    }

    const newVideo = document.createElement('video');
    newVideo.autoplay = true;
    newVideo.playsInline = true;
    newVideo.muted = true;
    newVideo.setAttribute('playsinline', 'true');
    newVideo.setAttribute('webkit-playsinline', 'true');
    newVideo.controls = false;
    newVideo.style.position = 'absolute';
    newVideo.style.top = '0px';
    newVideo.style.left = '0px';
    newVideo.style.width = '1px';
    newVideo.style.height = '1px';
    newVideo.style.opacity = '0.01';
    newVideo.style.pointerEvents = 'none';
    newVideo.style.zIndex = '-1000';

    document.body.appendChild(newVideo);
    this.videoRefs[peerId] = newVideo;

    if (peer.stream) {
      newVideo.srcObject = peer.stream;
      newVideo.load();
      newVideo.play(); //.catch(e => this.addLog('error', 'Compositor', `Recreation play failed for peer ${peerId}`, e));
    }
  }

  /**
   * Remove peer video element
   */
  removePeerVideo(peerId: string): void {
    const video = this.videoRefs[peerId];
    if (video && video.parentNode) {
      video.parentNode.removeChild(video);
    }
    delete this.videoRefs[peerId];
  }

  /**
   * Get all video elements
   */
  getAllVideos(): { local: HTMLVideoElement | null; peers: Record<string, HTMLVideoElement> } {
    return {
      local: this.localVideoRef,
      peers: { ...this.videoRefs }
    };
  }

  /**
   * Get ready videos for rendering
   */
  getReadyVideos(): HTMLVideoElement[] {
    const readyVideos: HTMLVideoElement[] = [];

    // Add local video if ready
    if (this.localVideoRef) {
      const isReady = this.localVideoRef.readyState >= this.localVideoRef.HAVE_CURRENT_DATA;
      if (Math.random() < 0.01) { // Occasional logging
        console.log(`🎥 Local video ready check: exists=${!!this.localVideoRef}, ready=${isReady}, state=${this.localVideoRef.readyState}, playing=${!this.localVideoRef.paused}`);
      }
      if (isReady) {
        readyVideos.push(this.localVideoRef);
      }
    }

    // Add peer videos that are ready
    Object.values(this.videoRefs).forEach(video => {
      if (video.readyState >= video.HAVE_CURRENT_DATA && video.videoWidth > 0 && video.videoHeight > 0) {
        readyVideos.push(video);
      }
    });

    return readyVideos;
  }

  /**
   * Get video ID for a video element
   */
  getVideoId(video: HTMLVideoElement): string {
    if (video === this.localVideoRef) {
      return 'local';
    }

    for (const [peerId, peerVideo] of Object.entries(this.videoRefs)) {
      if (peerVideo === video) {
        return peerId;
      }
    }

    return 'unknown';
  }

  /**
   * Clean up all video elements
   */
  cleanup(): void {
    // Clean up local video
    this.removeLocalVideo();

    // Clean up peer videos
    Object.keys(this.videoRefs).forEach(peerId => {
      this.removePeerVideo(peerId);
    });

    // Clean up overlay video
    if (this.overlayVideoRef) {
      if (this.overlayVideoRef.parentNode) {
        this.overlayVideoRef.parentNode.removeChild(this.overlayVideoRef);
      }
      this.overlayVideoRef.src = '';
      this.overlayVideoRef = null;
    }

    // Clean up logo image
    if (this.logoImageRef && this.logoImageRef.parentNode) {
      this.logoImageRef.parentNode.removeChild(this.logoImageRef);
      this.logoImageRef = null;
    }
  }
}
