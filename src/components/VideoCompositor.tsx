import React, { useEffect, useRef, useState, useCallback } from 'react';
import {
  LayoutMode,
  calculateVideoViewport,
  calculateOverlayViewport,
  calculateLogoViewport
} from './webgl/layoutUtils';
import { WebGLEngine } from './webgl/WebGLEngine';
import { VideoElementManager } from './video/VideoElementManager';
import { OverlayManager } from './video/OverlayManager';
import { LayoutControls } from './ui/LayoutControls';
import { StatusOverlay } from './ui/StatusOverlay';

interface VideoCompositorProps {
  peers: Record<string, any>;
  peerIds: string[];
  localStream: MediaStream | null;
  overlayVideoUrl?: string;
  showOverlays?: boolean;
  layoutMode: LayoutMode;
  onLayoutModeChange: (mode: LayoutMode) => void;
  onCanvasReady?: (canvas: HTMLCanvasElement) => void;
}



export const VideoCompositor: React.FC<VideoCompositorProps> = ({
  peers,
  peerIds,
  localStream,
  overlayVideoUrl,
  showOverlays = false,
  layoutMode: externalLayoutMode,
  onLayoutModeChange,
  onCanvasReady
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [internalLayoutMode, setInternalLayoutMode] = useState<LayoutMode>('grid');

  // Use external layout mode if provided, otherwise use internal state
  const layoutMode = showOverlays ? internalLayoutMode : externalLayoutMode;

  const webglEngineRef = useRef<WebGLEngine | null>(null);
  const videoManagerRef = useRef<VideoElementManager | null>(null);
  const overlayManagerRef = useRef<OverlayManager | null>(null);

  // Initialize managers
  useEffect(() => {
    if (!videoManagerRef.current) {
      videoManagerRef.current = new VideoElementManager();
    }

    if (!overlayManagerRef.current) {
      overlayManagerRef.current = new OverlayManager();
      // Set up logo image immediately
      overlayManagerRef.current.setupLogoImage();
    }
  }, []);







  const initWebGL = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) {
      return false;
    }

    if (!webglEngineRef.current) {
      webglEngineRef.current = new WebGLEngine();
    }

    const result = webglEngineRef.current.initialize(canvas);
    return result;
  }, []);









  // Handle overlay video
  useEffect(() => {
    const overlayManager = overlayManagerRef.current;
    if (!overlayManager) return;

    if (overlayVideoUrl) {
      overlayManager.setupOverlayVideo(overlayVideoUrl);
    } else {
      overlayManager.removeOverlayVideo();
    }
  }, [overlayVideoUrl]);



  // Handle local stream
  useEffect(() => {
    const videoManager = videoManagerRef.current;
    if (!videoManager) return;

    console.log(`🎥 VideoCompositor: localStream available: ${!!localStream}`);
    if (localStream) {
      console.log(`🎥 VideoCompositor: Creating local video with ${localStream.getTracks().length} tracks`);
      videoManager.getOrCreateLocalVideo(localStream);
    } else {
      console.log(`🎥 VideoCompositor: Removing local video`);
      videoManager.removeLocalVideo();
    }
  }, [localStream]);

  // Handle peer streams
  useEffect(() => {
    const videoManager = videoManagerRef.current;
    if (!videoManager) return;

    // Create or update video elements for each peer
    Object.entries(peers).forEach(([peerId, peer]) => {
      videoManager.getOrCreatePeerVideo(peerId, peer);
    });

    // Clean up removed peers
    const { peers: currentPeerVideos } = videoManager.getAllVideos();
    Object.keys(currentPeerVideos).forEach(peerId => {
      if (!peers[peerId]) {
        videoManager.removePeerVideo(peerId);
      }
    });
  }, [peers, peerIds]);



  // Initialize WebGL on mount
  useEffect(() => {
    if (!initWebGL()) {
      console.error('❌ WebGL initialization failed, using 2D canvas fallback');
      // Test 2D canvas as fallback
      const canvas = canvasRef.current;
      if (canvas) {
        const ctx = canvas.getContext('2d');
        if (ctx) {
          ctx.fillStyle = '#ff00ff'; // Magenta
          ctx.fillRect(0, 0, canvas.width, canvas.height);
        } else {
          console.error('❌ 2D canvas context also failed');
        }
        // Notify parent that canvas is ready
        if (onCanvasReady) {
          onCanvasReady(canvas);
        }
      }
      return;
    }
    console.log('✅ WebGL initialized successfully');

    // Notify parent that canvas is ready
    const canvas = canvasRef.current;
    if (canvas && onCanvasReady) {
      onCanvasReady(canvas);
    }
  }, [initWebGL, onCanvasReady]);

  // Main rendering loop
  useEffect(() => {
    const canvas = canvasRef.current;
    const engine = webglEngineRef.current;

    if (!canvas) {
      return;
    }

    if (!engine) {
      return;
    }

    const draw = () => {
      const videoManager = videoManagerRef.current;
      const engine = webglEngineRef.current;

      if (!videoManager) {
        return;
      }

      if (!engine) {
        return;
      }

      // Collect all available videos (local + peers)
      const allVideos = videoManager.getReadyVideos();

      if (allVideos.length === 0) {
        // Clear the canvas with gray background
        engine.clear(0.22, 0.25, 0.31, 1.0);
        // Occasionally warn about no videos
        if (Math.random() < 0.001) {
          console.warn('⚠️ No videos ready for rendering');
        }
      } else {
        // Clear the canvas for video rendering
        engine.clear(0.0, 0.0, 0.0, 1.0);
      }

      // Update textures and render videos
      allVideos.forEach((video, index) => {
        // Determine video ID using the video manager
        const videoId = videoManager.getVideoId(video);

        // Get or create texture for this video
        let texture = engine.getTexture(videoId);
        if (!texture) {
          const newTexture = engine.createVideoTexture(videoId, video);
          if (newTexture) {
            texture = newTexture;
          } else {
            return;
          }
        }

        if (texture && video.videoWidth > 0 && video.videoHeight > 0) {
          try {
            // Update texture with current video frame
            engine.updateVideoTexture(texture, video);

            // Calculate viewport based on layout
            const canvas = canvasRef.current!;
            const viewport = calculateVideoViewport(index, allVideos.length, canvas.width, canvas.height, layoutMode);

            // Render the video with aspect ratio preservation
            const borderColor: [number, number, number] = videoId === 'local' ? [0, 0.83, 1] : [0.54, 0.31, 0.96]; // Blue for local, purple for peers
            engine.renderVideo(texture, viewport, borderColor, 1.0, 0.02, video.videoWidth, video.videoHeight);
          } catch (error) {
            console.error(`❌ Failed to render video ${videoId}:`, error);
          }
        }
      });

      // Render overlay video on top if available
      const overlayManager = overlayManagerRef.current;
      if (overlayManager && overlayManager.isOverlayVideoReady()) {
        const overlayVideo = overlayManager.getOverlayVideo()!;
        const overlayVideoId = 'overlay';

        // Get or create texture for overlay video
        let overlayTexture = engine.getTexture(overlayVideoId);
        if (!overlayTexture) {
          const newTexture = engine.createVideoTexture(overlayVideoId, overlayVideo);
          if (newTexture) {
            overlayTexture = newTexture;
          }
        }

        if (overlayTexture) {
          try {
            // Update texture with current overlay video frame
            engine.updateVideoTexture(overlayTexture, overlayVideo);

            // Calculate overlay viewport (bottom-right, 50% width and height)
            const overlayViewport = calculateOverlayViewport(canvas.width, canvas.height);

            // Render the overlay video with a distinct border color (green) and aspect ratio preservation
            engine.renderVideo(overlayTexture, overlayViewport, [0, 1, 0.5], 1.0, 0.02, overlayVideo.videoWidth, overlayVideo.videoHeight); // Green border, full opacity
          } catch (error) {
            console.error('❌ Failed to render overlay video:', error);
          }
        }
      }

      // Always try to render logo image on top if available (even with no videos)
      if (overlayManager) {
        const logoImage = overlayManager.getLogoImage();

        if (!logoImage) {
          // Try to setup logo again
          overlayManager.setupLogoImage();
        }
      }

      if (overlayManager && overlayManager.isLogoImageReady()) {
        const logoImage = overlayManager.getLogoImage()!;
        const logoId = 'logo';

        // Get or create texture for logo image
        let logoTexture = engine.getTexture(logoId);
        if (!logoTexture) {
          const newTexture = engine.createImageTexture(logoId, logoImage);
          if (newTexture) {
            logoTexture = newTexture;
          }
        }

        if (logoTexture) {
          try {
            // Calculate logo viewport (top-right, 10% size)
            const logoViewport = calculateLogoViewport(canvas.width, canvas.height);

            // Render the logo with no border and aspect ratio preservation
            engine.renderVideo(logoTexture, logoViewport, [0, 0, 0], 1.0, 0, logoImage.width, logoImage.height); // No border, full opacity
          } catch (error) {
            console.error('❌ Failed to render logo:', error);
          }
        }
      }
    };

    const animate = () => {
      draw();
      requestAnimationFrame(animate);
    };

    animate();

    // Cleanup function
    return () => {
      // Clean up WebGL textures
      if (engine) {
        engine.cleanup();
      }

      // Clean up video manager
      if (videoManagerRef.current) {
        videoManagerRef.current.cleanup();
      }

      // Clean up overlay manager
      if (overlayManagerRef.current) {
        overlayManagerRef.current.cleanup();
      }
    };
  }, [layoutMode]);

  return (
    <div className="relative bg-gray-900 rounded-xl overflow-hidden">
      {/* Layout Controls */}
      <LayoutControls
        layoutMode={layoutMode}
        onLayoutModeChange={setInternalLayoutMode}
        showOverlays={showOverlays}
      />

      {/* Composite Canvas */}
      <canvas
        ref={canvasRef}
        width={1920}
        height={1080}
        className="w-full aspect-video"
      />

      {/* Status Overlay */}
      <StatusOverlay
        layoutMode={layoutMode}
        peerCount={Object.keys(peers).length}
        hasLocalStream={!!localStream}
        showOverlays={showOverlays}
      />
    </div>
  );
};
