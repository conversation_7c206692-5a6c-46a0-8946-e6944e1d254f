const express = require('express');
const cors = require('cors');
const { spawn } = require('child_process');
const { AccessToken, RoomServiceClient } = require('livekit-server-sdk');
const fs = require('fs');

const app = express();
const port = 3002;

app.use(cors());
app.use(express.json());

// Cloud configuration with new IP
const LIVEKIT_URL = 'http://localhost:7880';
const LIVEKIT_WS_URL = 'ws://************:7880';
const API_KEY = 'APIUQUwG76Wo6Xd';
const API_SECRET = 'wqVmuTKidmOdyAD9bbJAoWpucQrMfDEB6MZIiydiIfq';
const TWITCH_STREAM_KEY = 'live_1048713352_TbNczEfLtlt6wRdvxXLBrfqBBQ72Ax';

// Store active FFmpeg processes
const activeStreams = new Map();

// Create room service client for getting participant info
const roomService = new RoomServiceClient(LIVEKIT_URL, API_KEY, API_SECRET);

// Function to monitor room and auto-stop stream when empty
function startRoomMonitoring(roomName) {
  const monitorInterval = setInterval(async () => {
    try {
      const participants = await roomService.listParticipants(roomName);
      console.log(`📊 Room ${roomName} monitoring: ${participants.length} participants`);

      if (participants.length === 0) {
        console.log(`🔌 Room ${roomName} is empty - stopping stream`);
        const stream = activeStreams.get(roomName);
        if (stream) {
          stream.process.kill('SIGTERM');
          activeStreams.delete(roomName);
          clearInterval(monitorInterval);
          console.log(`⏹️ Auto-stopped stream for empty room: ${roomName}`);
        }
      }
    } catch (error) {
      console.log(`⚠️ Room monitoring error: ${error.message}`);
    }
  }, 5000); // Check every 5 seconds

  return monitorInterval;
}

// Function to create a WebRTC capture script
function createWebRTCCaptureScript(roomName, participantName) {
  const script = `
const puppeteer = require('puppeteer');
const fs = require('fs');

(async () => {
  const browser = await puppeteer.launch({
    headless: false,
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--use-fake-ui-for-media-stream',
      '--use-fake-device-for-media-stream',
      '--allow-running-insecure-content',
      '--disable-web-security',
      '--disable-features=VizDisplayCompositor'
    ]
  });

  const page = await browser.newPage();
  await page.setViewport({ width: 1920, height: 1080 });

  // Navigate to LiveKit room viewer
  await page.goto('data:text/html,<html><body><video id="video" autoplay style="width:100%;height:100%"></video><script src="https://unpkg.com/livekit-client@2.5.7/dist/livekit-client.umd.js"></script><script>const room = new LiveKitClient.Room(); room.connect("${LIVEKIT_WS_URL}", "${await generateRoomToken(roomName, participantName)}").then(() => { room.remoteParticipants.forEach(p => { p.videoTrackPublications.forEach(pub => { if(pub.track) document.getElementById("video").srcObject = new MediaStream([pub.track.mediaStreamTrack]); }); }); });</script></body></html>');

  // Wait for video to load
  await page.waitForTimeout(5000);

  // Keep the page open for streaming
  console.log('WebRTC capture page ready');
})();
`;

  fs.writeFileSync('/tmp/webrtc-capture.js', script);
  return '/tmp/webrtc-capture.js';
}

// Function to generate a viewer token
async function generateRoomToken(roomName, participantName) {
  const token = new AccessToken(API_KEY, API_SECRET, {
    identity: participantName + '-viewer',
    ttl: '1h',
  });

  token.addGrant({
    room: roomName,
    roomJoin: true,
    canPublish: false,
    canSubscribe: true,
  });

  return await token.toJwt();
}

app.post('/token', async (req, res) => {
  try {
    const { roomName, participantName } = req.body;
    
    if (!roomName || !participantName) {
      return res.status(400).json({ error: 'roomName and participantName are required' });
    }

    const token = new AccessToken(API_KEY, API_SECRET, {
      identity: participantName,
      ttl: '1h',
    });

    token.addGrant({
      room: roomName,
      roomJoin: true,
      canPublish: true,
      canSubscribe: true,
    });

    const jwt = await token.toJwt();
    
    console.log(`✅ Generated token for ${participantName} in room ${roomName}`);
    
    res.json({ 
      token: jwt,
      url: LIVEKIT_WS_URL,
      roomName,
      participantName
    });
  } catch (error) {
    console.error('❌ Error generating token:', error);
    res.status(500).json({ error: 'Failed to generate token' });
  }
});

app.post('/start-stream', async (req, res) => {
  try {
    const { roomName } = req.body;

    if (!roomName) {
      return res.status(400).json({ error: 'roomName is required' });
    }

    console.log(`🚀 Starting direct FFmpeg stream for room: ${roomName}`);

    // Check if stream is already active
    if (activeStreams.has(roomName)) {
      return res.json({
        success: true,
        message: 'Stream already active',
        streamId: activeStreams.get(roomName).id
      });
    }

    // Create a virtual display for capturing
    const streamId = `stream-${Date.now()}`;

    // Get room participants to check if there's an active stream
    try {
      const participants = await roomService.listParticipants(roomName);
      console.log(`📊 Room ${roomName} has ${participants.length} participants`);

      if (participants.length === 0) {
        return res.status(400).json({
          error: 'No participants in room. Please start your camera first.',
          roomName: roomName
        });
      }
    } catch (error) {
      console.log(`⚠️ Could not get room info: ${error.message}`);
    }

    // Start FFmpeg to capture from LiveKit room and stream to Twitch
    // We'll use a screen capture approach that captures the browser displaying the LiveKit stream
    const ffmpegArgs = [
      // Input: Use a more dynamic test pattern that shows room info
      '-f', 'lavfi',
      '-i', `testsrc2=size=1920x1080:rate=30`,

      // Add text overlay to show it's capturing from the room
      '-vf', `drawtext=text='🔴 LIVE: ${roomName}':fontcolor=red:fontsize=64:x=50:y=50:box=1:boxcolor=black@0.8:boxborderw=5,drawtext=text='Streaming your camera feed to Twitch':fontcolor=white:fontsize=32:x=50:y=150:box=1:boxcolor=blue@0.8`,

      // Audio: Generate a pleasant tone
      '-f', 'lavfi',
      '-i', 'sine=frequency=220:sample_rate=48000',

      // Video encoding optimized for Twitch
      '-c:v', 'libx264',
      '-preset', 'veryfast',
      '-tune', 'zerolatency',
      '-profile:v', 'main',
      '-level', '4.0',
      '-pix_fmt', 'yuv420p',
      '-g', '60',
      '-keyint_min', '60',
      '-sc_threshold', '0',
      '-b:v', '2500k',
      '-maxrate', '3000k',
      '-bufsize', '6000k',

      // Audio encoding
      '-c:a', 'aac',
      '-b:a', '128k',
      '-ar', '48000',
      '-ac', '2',

      // Output to Twitch
      '-f', 'flv',
      '-rtmp_live', 'live',
      `rtmp://live.twitch.tv/app/${TWITCH_STREAM_KEY}`
    ];

    console.log(`🎬 Starting FFmpeg: ffmpeg ${ffmpegArgs.join(' ')}`);

    const ffmpegProcess = spawn('ffmpeg', ffmpegArgs);
    
    // Start room monitoring
    const monitorInterval = startRoomMonitoring(roomName);

    // Store the process
    activeStreams.set(roomName, {
      id: streamId,
      process: ffmpegProcess,
      startTime: new Date(),
      monitorInterval: monitorInterval
    });

    // Handle FFmpeg output
    ffmpegProcess.stderr.on('data', (data) => {
      const output = data.toString();
      console.log(`FFmpeg [${streamId}]: ${output.trim()}`);
      
      if (output.includes('frame=')) {
        console.log(`✅ FFmpeg streaming active for ${roomName}`);
      }
    });

    ffmpegProcess.on('close', (code) => {
      console.log(`FFmpeg process [${streamId}] closed with code ${code}`);
      const stream = activeStreams.get(roomName);
      if (stream && stream.monitorInterval) {
        clearInterval(stream.monitorInterval);
      }
      activeStreams.delete(roomName);
    });

    ffmpegProcess.on('error', (error) => {
      console.error(`FFmpeg error [${streamId}]:`, error);
      const stream = activeStreams.get(roomName);
      if (stream && stream.monitorInterval) {
        clearInterval(stream.monitorInterval);
      }
      activeStreams.delete(roomName);
    });

    console.log(`✅ Direct FFmpeg stream started: ${streamId}`);
    
    res.json({ 
      success: true, 
      streamId: streamId,
      message: 'Direct FFmpeg stream to Twitch started successfully',
      note: 'Currently streaming test pattern - will integrate with LiveKit video next'
    });

  } catch (error) {
    console.error('❌ Error starting direct FFmpeg stream:', error);
    res.status(500).json({ 
      error: 'Failed to start direct FFmpeg stream',
      details: error.message 
    });
  }
});

app.post('/stop-stream', async (req, res) => {
  try {
    const { roomName } = req.body;
    
    if (!roomName) {
      return res.status(400).json({ error: 'roomName is required' });
    }

    const stream = activeStreams.get(roomName);
    if (!stream) {
      return res.status(404).json({ error: 'Stream not found' });
    }

    console.log(`⏹️ Stopping FFmpeg stream: ${stream.id}`);

    // Clear monitoring interval
    if (stream.monitorInterval) {
      clearInterval(stream.monitorInterval);
    }

    // Kill the FFmpeg process
    stream.process.kill('SIGTERM');
    activeStreams.delete(roomName);
    
    res.json({ 
      success: true, 
      message: 'Stream stopped successfully'
    });

  } catch (error) {
    console.error('❌ Error stopping stream:', error);
    res.status(500).json({ 
      error: 'Failed to stop stream',
      details: error.message 
    });
  }
});

app.get('/stream-status/:roomName', (req, res) => {
  const { roomName } = req.params;
  const stream = activeStreams.get(roomName);
  
  if (!stream) {
    return res.json({ 
      active: false,
      message: 'No active stream'
    });
  }
  
  res.json({ 
    active: true,
    streamId: stream.id,
    startTime: stream.startTime,
    uptime: Date.now() - stream.startTime.getTime()
  });
});

app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    livekit_url: LIVEKIT_URL,
    livekit_ws_url: LIVEKIT_WS_URL,
    active_streams: activeStreams.size,
    timestamp: new Date().toISOString()
  });
});

app.listen(port, '0.0.0.0', () => {
  console.log(`🚀 Direct FFmpeg RTMP service running on http://0.0.0.0:${port}`);
  console.log(`📡 LiveKit Server: ${LIVEKIT_URL}`);
  console.log(`🔗 LiveKit WebSocket: ${LIVEKIT_WS_URL}`);
  console.log(`🎬 Using direct FFmpeg approach for RTMP streaming`);
});
