const express = require('express');
const cors = require('cors');
const { spawn } = require('child_process');
const { AccessToken } = require('livekit-server-sdk');

const app = express();
const port = 3002;

app.use(cors());
app.use(express.json());

// Cloud configuration with current IP
const LIVEKIT_URL = 'http://localhost:7880';
const LIVEKIT_WS_URL = 'ws://************:7880';
const API_KEY = 'APIUQUwG76Wo6Xd';
const API_SECRET = 'wqVmuTKidmOdyAD9bbJAoWpucQrMfDEB6MZIiydiIfq';
const TWITCH_STREAM_KEY = 'live_1048713352_TbNczEfLtlt6wRdvxXLBrfqBBQ72Ax';

// Store active FFmpeg processes
const activeStreams = new Map();

app.post('/token', async (req, res) => {
  try {
    const { roomName, participantName } = req.body;
    
    if (!roomName || !participantName) {
      return res.status(400).json({ error: 'roomName and participantName are required' });
    }

    const token = new AccessToken(API_KEY, API_SECRET, {
      identity: participantName,
      ttl: '1h',
    });

    token.addGrant({
      room: roomName,
      roomJoin: true,
      canPublish: true,
      canSubscribe: true,
    });

    const jwt = await token.toJwt();
    
    console.log(`✅ Generated token for ${participantName} in room ${roomName}`);
    
    res.json({ 
      token: jwt,
      url: LIVEKIT_WS_URL,
      roomName,
      participantName
    });
  } catch (error) {
    console.error('❌ Error generating token:', error);
    res.status(500).json({ error: 'Failed to generate token' });
  }
});

app.post('/start-stream', async (req, res) => {
  try {
    const { roomName } = req.body;
    
    if (!roomName) {
      return res.status(400).json({ error: 'roomName is required' });
    }

    console.log(`🚀 Starting simple FFmpeg stream for room: ${roomName}`);

    // Check if stream is already active
    if (activeStreams.has(roomName)) {
      return res.json({
        success: true,
        message: 'Stream already active',
        streamId: activeStreams.get(roomName).id
      });
    }

    const streamId = `stream-${Date.now()}`;
    
    // Simple working FFmpeg command
    const ffmpegArgs = [
      // Simple test pattern
      '-f', 'lavfi',
      '-i', 'testsrc=size=1920x1080:rate=30',
      
      // Simple audio
      '-f', 'lavfi', 
      '-i', 'sine=frequency=440:sample_rate=48000',
      
      // Video encoding for Twitch
      '-c:v', 'libx264',
      '-preset', 'ultrafast',
      '-tune', 'zerolatency',
      '-profile:v', 'baseline',
      '-level', '3.1',
      '-pix_fmt', 'yuv420p',
      '-g', '60',
      '-keyint_min', '60',
      '-sc_threshold', '0',
      '-b:v', '2500k',
      '-maxrate', '3000k',
      '-bufsize', '6000k',
      
      // Audio encoding
      '-c:a', 'aac',
      '-b:a', '128k',
      '-ar', '48000',
      '-ac', '2',
      
      // Output to Twitch
      '-f', 'flv',
      '-rtmp_live', 'live',
      `rtmp://live.twitch.tv/app/${TWITCH_STREAM_KEY}`
    ];

    console.log(`🎬 Starting simple FFmpeg: ffmpeg ${ffmpegArgs.join(' ')}`);

    const ffmpegProcess = spawn('ffmpeg', ffmpegArgs);
    
    // Store the process
    activeStreams.set(roomName, {
      id: streamId,
      process: ffmpegProcess,
      startTime: new Date()
    });

    // Handle FFmpeg output
    ffmpegProcess.stderr.on('data', (data) => {
      const output = data.toString();
      console.log(`FFmpeg [${streamId}]: ${output.trim()}`);
      
      if (output.includes('frame=')) {
        console.log(`✅ FFmpeg streaming active for ${roomName}`);
      }
    });

    ffmpegProcess.on('close', (code) => {
      console.log(`FFmpeg process [${streamId}] closed with code ${code}`);
      activeStreams.delete(roomName);
    });

    ffmpegProcess.on('error', (error) => {
      console.error(`FFmpeg error [${streamId}]:`, error);
      activeStreams.delete(roomName);
    });

    console.log(`✅ Simple FFmpeg stream started: ${streamId}`);
    
    res.json({ 
      success: true, 
      streamId: streamId,
      message: 'Simple FFmpeg stream to Twitch started successfully'
    });

  } catch (error) {
    console.error('❌ Error starting simple FFmpeg stream:', error);
    res.status(500).json({ 
      error: 'Failed to start simple FFmpeg stream',
      details: error.message 
    });
  }
});

app.post('/stop-stream', async (req, res) => {
  try {
    const { roomName } = req.body;
    
    if (!roomName) {
      return res.status(400).json({ error: 'roomName is required' });
    }

    const stream = activeStreams.get(roomName);
    if (!stream) {
      return res.status(404).json({ error: 'Stream not found' });
    }

    console.log(`⏹️ Stopping FFmpeg stream: ${stream.id}`);
    
    stream.process.kill('SIGTERM');
    activeStreams.delete(roomName);
    
    res.json({ 
      success: true, 
      message: 'Stream stopped successfully'
    });

  } catch (error) {
    console.error('❌ Error stopping stream:', error);
    res.status(500).json({ 
      error: 'Failed to stop stream',
      details: error.message 
    });
  }
});

app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    livekit_url: LIVEKIT_URL,
    livekit_ws_url: LIVEKIT_WS_URL,
    active_streams: activeStreams.size,
    service_type: 'Simple FFmpeg Service',
    timestamp: new Date().toISOString()
  });
});

app.listen(port, '0.0.0.0', () => {
  console.log(`🚀 Simple FFmpeg RTMP service running on http://0.0.0.0:${port}`);
  console.log(`📡 LiveKit Server: ${LIVEKIT_URL}`);
  console.log(`🔗 LiveKit WebSocket: ${LIVEKIT_WS_URL}`);
  console.log(`🎬 Using simple FFmpeg approach for RTMP streaming`);
});
