const { spawn } = require('child_process');
const { PassThrough } = require('stream');

/**
 * WebRTC to FFmpeg Bridge
 * Handles the complex task of converting WebRTC MediaStream to FFmpeg input
 */
class WebRTCFFmpegBridge {
  constructor(sessionId, rtmpUrl, options = {}) {
    this.sessionId = sessionId;
    this.rtmpUrl = rtmpUrl;
    this.options = {
      width: options.width || 1920,
      height: options.height || 1080,
      framerate: options.framerate || 30,
      videoBitrate: options.videoBitrate || '2500k',
      audioBitrate: options.audioBitrate || '128k',
      audioSampleRate: options.audioSampleRate || 44100,
      audioChannels: options.audioChannels || 2,
      ...options
    };
    
    this.ffmpegProcess = null;
    this.videoStream = new PassThrough();
    this.audioStream = new PassThrough();
    this.isRunning = false;
    this.startTime = null;
  }

  /**
   * Start the FFmpeg process with optimized settings for RTMP streaming
   */
  start() {
    if (this.isRunning) {
      console.warn(`Bridge ${this.sessionId} is already running`);
      return;
    }

    console.log(`🎬 Starting FFmpeg bridge for session ${this.sessionId}`);
    
    const ffmpegArgs = [
      // Input settings for video
      '-f', 'rawvideo',
      '-pixel_format', 'yuv420p',
      '-video_size', `${this.options.width}x${this.options.height}`,
      '-framerate', this.options.framerate.toString(),
      '-i', 'pipe:0', // Video input from stdin
      
      // Input settings for audio (if available)
      '-f', 's16le',
      '-ar', this.options.audioSampleRate.toString(),
      '-ac', this.options.audioChannels.toString(),
      '-i', 'pipe:1', // Audio input from second pipe
      
      // Video encoding settings
      '-c:v', 'libx264',
      '-preset', 'veryfast',
      '-tune', 'zerolatency',
      '-profile:v', 'baseline',
      '-level', '3.0',
      '-pix_fmt', 'yuv420p',
      '-g', '60', // GOP size (keyframe interval)
      '-keyint_min', '60',
      '-sc_threshold', '0',
      '-b:v', this.options.videoBitrate,
      '-maxrate', this.options.videoBitrate,
      '-bufsize', `${parseInt(this.options.videoBitrate) * 2}k`,
      
      // Audio encoding settings
      '-c:a', 'aac',
      '-b:a', this.options.audioBitrate,
      '-ar', this.options.audioSampleRate.toString(),
      '-ac', this.options.audioChannels.toString(),
      
      // Output format settings
      '-f', 'flv',
      '-flvflags', 'no_duration_filesize',
      
      // RTMP specific settings
      '-rtmp_live', 'live',
      '-rtmp_buffer', '1000',
      
      // Output URL
      this.rtmpUrl
    ];

    console.log(`FFmpeg command: ffmpeg ${ffmpegArgs.join(' ')}`);

    this.ffmpegProcess = spawn('ffmpeg', ffmpegArgs, {
      stdio: ['pipe', 'pipe', 'pipe', 'pipe', 'pipe']
    });

    this.isRunning = true;
    this.startTime = new Date();

    // Handle FFmpeg events
    this.ffmpegProcess.on('spawn', () => {
      console.log(`✅ FFmpeg process spawned for session ${this.sessionId}`);
    });

    this.ffmpegProcess.on('error', (error) => {
      console.error(`❌ FFmpeg process error for session ${this.sessionId}:`, error);
      this.isRunning = false;
    });

    this.ffmpegProcess.on('exit', (code, signal) => {
      console.log(`🏁 FFmpeg process exited for session ${this.sessionId} with code ${code}, signal ${signal}`);
      this.isRunning = false;
    });

    // Handle stderr for logging
    this.ffmpegProcess.stderr.on('data', (data) => {
      const message = data.toString();
      // Only log important messages to avoid spam
      if (message.includes('error') || message.includes('warning') || message.includes('fps=')) {
        console.log(`FFmpeg [${this.sessionId}]: ${message.trim()}`);
      }
    });

    // Handle stdout
    this.ffmpegProcess.stdout.on('data', (data) => {
      // FFmpeg output data (usually empty for RTMP)
    });

    return this.ffmpegProcess;
  }

  /**
   * Send video frame data to FFmpeg
   * @param {Buffer} frameData - Raw video frame data in YUV420P format
   */
  writeVideoFrame(frameData) {
    if (this.isRunning && this.ffmpegProcess && this.ffmpegProcess.stdin) {
      try {
        this.ffmpegProcess.stdin.write(frameData);
      } catch (error) {
        console.error(`Error writing video frame for session ${this.sessionId}:`, error);
      }
    }
  }

  /**
   * Send audio data to FFmpeg
   * @param {Buffer} audioData - Raw audio data in S16LE format
   */
  writeAudioData(audioData) {
    if (this.isRunning && this.ffmpegProcess && this.ffmpegProcess.stdio[3]) {
      try {
        this.ffmpegProcess.stdio[3].write(audioData);
      } catch (error) {
        console.error(`Error writing audio data for session ${this.sessionId}:`, error);
      }
    }
  }

  /**
   * Stop the FFmpeg process
   */
  stop() {
    if (!this.isRunning || !this.ffmpegProcess) {
      return;
    }

    console.log(`🛑 Stopping FFmpeg bridge for session ${this.sessionId}`);

    try {
      // Close input streams
      if (this.ffmpegProcess.stdin) {
        this.ffmpegProcess.stdin.end();
      }
      if (this.ffmpegProcess.stdio[3]) {
        this.ffmpegProcess.stdio[3].end();
      }

      // Give FFmpeg a moment to finish processing
      setTimeout(() => {
        if (this.ffmpegProcess && !this.ffmpegProcess.killed) {
          this.ffmpegProcess.kill('SIGTERM');
          
          // Force kill if it doesn't respond
          setTimeout(() => {
            if (this.ffmpegProcess && !this.ffmpegProcess.killed) {
              this.ffmpegProcess.kill('SIGKILL');
            }
          }, 5000);
        }
      }, 1000);

    } catch (error) {
      console.error(`Error stopping FFmpeg bridge for session ${this.sessionId}:`, error);
    }

    this.isRunning = false;
  }

  /**
   * Get the current status of the bridge
   */
  getStatus() {
    return {
      sessionId: this.sessionId,
      isRunning: this.isRunning,
      startTime: this.startTime,
      rtmpUrl: this.rtmpUrl,
      options: this.options,
      processId: this.ffmpegProcess?.pid || null
    };
  }
}

/**
 * Canvas to Raw Video Converter
 * Converts HTML5 Canvas frames to raw video data for FFmpeg
 */
class CanvasToVideoConverter {
  constructor(width = 1920, height = 1080) {
    this.width = width;
    this.height = height;
    this.canvas = null;
    this.context = null;
  }

  /**
   * Initialize the converter with a canvas element
   * @param {HTMLCanvasElement} canvas - The canvas element to convert
   */
  initialize(canvas) {
    this.canvas = canvas;
    this.context = canvas.getContext('2d');
    
    // Ensure canvas is the correct size
    if (canvas.width !== this.width || canvas.height !== this.height) {
      console.warn(`Canvas size (${canvas.width}x${canvas.height}) doesn't match expected size (${this.width}x${this.height})`);
    }
  }

  /**
   * Convert current canvas frame to raw YUV420P data
   * @returns {Buffer} Raw video frame data
   */
  getFrameData() {
    if (!this.canvas || !this.context) {
      throw new Error('Converter not initialized');
    }

    // Get image data from canvas
    const imageData = this.context.getImageData(0, 0, this.width, this.height);
    const rgbaData = imageData.data;

    // Convert RGBA to YUV420P
    const yuvData = this.rgbaToYuv420p(rgbaData, this.width, this.height);
    
    return Buffer.from(yuvData);
  }

  /**
   * Convert RGBA pixel data to YUV420P format
   * @param {Uint8ClampedArray} rgbaData - RGBA pixel data
   * @param {number} width - Image width
   * @param {number} height - Image height
   * @returns {Uint8Array} YUV420P data
   */
  rgbaToYuv420p(rgbaData, width, height) {
    const ySize = width * height;
    const uvSize = (width * height) / 4;
    const yuvData = new Uint8Array(ySize + uvSize * 2);

    let yIndex = 0;
    let uIndex = ySize;
    let vIndex = ySize + uvSize;

    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const rgbaIndex = (y * width + x) * 4;
        const r = rgbaData[rgbaIndex];
        const g = rgbaData[rgbaIndex + 1];
        const b = rgbaData[rgbaIndex + 2];

        // Convert RGB to YUV
        const yValue = Math.round(0.299 * r + 0.587 * g + 0.114 * b);
        yuvData[yIndex++] = yValue;

        // Sample U and V at half resolution (4:2:0 subsampling)
        if (y % 2 === 0 && x % 2 === 0) {
          const uValue = Math.round(-0.169 * r - 0.331 * g + 0.5 * b + 128);
          const vValue = Math.round(0.5 * r - 0.419 * g - 0.081 * b + 128);
          
          yuvData[uIndex++] = Math.max(0, Math.min(255, uValue));
          yuvData[vIndex++] = Math.max(0, Math.min(255, vValue));
        }
      }
    }

    return yuvData;
  }
}

module.exports = {
  WebRTCFFmpegBridge,
  CanvasToVideoConverter
};
