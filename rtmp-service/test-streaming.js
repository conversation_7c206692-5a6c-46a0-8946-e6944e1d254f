#!/usr/bin/env node

const io = require('socket.io-client');

console.log('🧪 Testing RTMP streaming service...');

// Connect to RTMP service
const socket = io('http://localhost:3002', {
  transports: ['websocket', 'polling']
});

socket.on('connect', () => {
  console.log('✅ Connected to RTMP service');
  console.log(`🆔 Socket ID: ${socket.id}`);
  
  // Start streaming
  console.log('🚀 Starting stream...');
  socket.emit('start-stream', {
    rtmpUrl: 'rtmp://live.twitch.tv/app',
    streamKey: 'live_1048713352_TbNczEfLtlt6wRdvxXLBrfqBBQ72Ax',
    roomId: 'test-room'
  });
});

socket.on('stream-session-created', ({ sessionId, success, error }) => {
  if (success) {
    console.log(`✅ Stream session created: ${sessionId}`);
    console.log('🎬 Animated test pattern should now be streaming to Twitch!');
    console.log('📺 Check your Twitch stream to see the moving gradient pattern');
    
    // Keep the connection alive for a while
    setTimeout(() => {
      console.log('🛑 Stopping test...');
      socket.emit('stop-stream', { sessionId });
    }, 30000); // Run for 30 seconds
  } else {
    console.error(`❌ Failed to create stream session: ${error}`);
    process.exit(1);
  }
});

socket.on('stream-stopped', ({ sessionId, success, error }) => {
  if (success) {
    console.log('✅ Stream stopped successfully');
  } else {
    console.error(`❌ Error stopping stream: ${error}`);
  }
  process.exit(0);
});

socket.on('connect_error', (error) => {
  console.error('❌ Connection error:', error);
  process.exit(1);
});

socket.on('disconnect', (reason) => {
  console.log(`🔌 Disconnected: ${reason}`);
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Stopping test...');
  socket.disconnect();
  process.exit(0);
});
