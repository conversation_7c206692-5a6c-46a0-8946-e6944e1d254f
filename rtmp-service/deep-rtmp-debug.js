#!/usr/bin/env node

const { spawn } = require('child_process');

console.log('🔍 Deep RTMP Protocol Debugging...\n');

const STREAM_KEY = 'live_1048713352_TbNczEfLtlt6wRdvxXLBrfqBBQ72Ax';

// Test with maximum debugging and different approaches
function testWithMaxDebug() {
  console.log('🧪 Testing with maximum RTMP debugging...\n');
  
  const args = [
    '-v', 'trace',  // Maximum verbosity
    '-report',      // Generate detailed report
    '-f', 'lavfi',
    '-i', 'testsrc=duration=5:size=1280x720:rate=30',
    '-f', 'lavfi',
    '-i', 'sine=frequency=1000:duration=5',
    '-c:v', 'libx264',
    '-preset', 'ultrafast',
    '-tune', 'zerolatency',
    '-profile:v', 'baseline',
    '-level', '3.1',
    '-pix_fmt', 'yuv420p',
    '-g', '60',
    '-keyint_min', '60',
    '-b:v', '800k',
    '-maxrate', '1000k',
    '-bufsize', '2000k',
    '-c:a', 'aac',
    '-b:a', '128k',
    '-ar', '44100',
    '-ac', '2',
    '-f', 'flv',
    '-rtmp_live', 'live',
    '-rtmp_buffer', '1000',
    '-rtmp_flush_interval', '10',
    `rtmp://live.twitch.tv/app/${STREAM_KEY}`
  ];
  
  console.log('FFmpeg command:');
  console.log(`ffmpeg ${args.join(' ')}\n`);
  
  const ffmpeg = spawn('ffmpeg', args);
  
  let allOutput = '';
  
  ffmpeg.stderr.on('data', (data) => {
    const output = data.toString();
    allOutput += output;
    
    // Look for specific RTMP protocol messages
    if (output.includes('RTMP') || 
        output.includes('handshake') || 
        output.includes('connect') ||
        output.includes('createStream') ||
        output.includes('publish') ||
        output.includes('NetConnection') ||
        output.includes('NetStream')) {
      console.log('🔍 RTMP Protocol:', output.trim());
    }
    
    if (output.includes('fps=')) {
      console.log('✅ SUCCESS! Stream is working!');
      ffmpeg.kill('SIGTERM');
      process.exit(0);
    }
    
    if (output.includes('Error opening output')) {
      console.log('❌ RTMP connection failed during handshake');
      
      // Analyze the full output for clues
      setTimeout(() => {
        analyzeFailure(allOutput);
        testAlternativeMethod();
      }, 1000);
    }
  });
  
  ffmpeg.on('close', (code) => {
    if (code !== 0) {
      console.log(`❌ FFmpeg exited with code: ${code}`);
      setTimeout(() => testAlternativeMethod(), 1000);
    }
  });
  
  // Timeout after 10 seconds
  setTimeout(() => {
    if (!ffmpeg.killed) {
      console.log('⏱️ Timeout - killing FFmpeg');
      ffmpeg.kill('SIGTERM');
      setTimeout(() => testAlternativeMethod(), 1000);
    }
  }, 10000);
}

function analyzeFailure(output) {
  console.log('\n📊 Analyzing failure...');
  
  if (output.includes('SSL') || output.includes('TLS')) {
    console.log('💡 SSL/TLS related issue detected');
  }
  
  if (output.includes('timeout')) {
    console.log('💡 Timeout issue detected');
  }
  
  if (output.includes('refused')) {
    console.log('💡 Connection refused - possible firewall/network issue');
  }
  
  if (output.includes('authentication') || output.includes('auth')) {
    console.log('💡 Authentication issue detected');
  }
  
  if (output.includes('protocol')) {
    console.log('💡 Protocol compatibility issue detected');
  }
}

function testAlternativeMethod() {
  console.log('\n🔄 Trying alternative approach: rtmps (secure RTMP)...\n');
  
  const args = [
    '-v', 'info',
    '-f', 'lavfi',
    '-i', 'testsrc=duration=5:size=1280x720:rate=30',
    '-f', 'lavfi',
    '-i', 'sine=frequency=1000:duration=5',
    '-c:v', 'libx264',
    '-preset', 'ultrafast',
    '-profile:v', 'baseline',
    '-level', '3.1',
    '-pix_fmt', 'yuv420p',
    '-g', '60',
    '-b:v', '800k',
    '-c:a', 'aac',
    '-b:a', '128k',
    '-ar', '44100',
    '-f', 'flv',
    `rtmps://live.twitch.tv:443/live/${STREAM_KEY}`
  ];
  
  console.log('Trying RTMPS (secure RTMP):');
  console.log(`ffmpeg ${args.join(' ')}\n`);
  
  const ffmpeg = spawn('ffmpeg', args);
  
  ffmpeg.stderr.on('data', (data) => {
    const output = data.toString();
    
    if (output.includes('fps=')) {
      console.log('✅ SUCCESS! RTMPS is working!');
      console.log('💡 Your system requires secure RTMP (RTMPS) instead of regular RTMP');
      ffmpeg.kill('SIGTERM');
      process.exit(0);
    }
    
    if (output.includes('Error') || output.includes('failed')) {
      console.log('🔍 RTMPS error:', output.trim());
    }
  });
  
  ffmpeg.on('close', (code) => {
    console.log(`❌ RTMPS also failed (code: ${code})`);
    testWithCurl();
  });
  
  setTimeout(() => {
    if (!ffmpeg.killed) {
      ffmpeg.kill('SIGTERM');
      testWithCurl();
    }
  }, 8000);
}

function testWithCurl() {
  console.log('\n🌐 Testing RTMP connectivity with curl...\n');
  
  // Test if we can at least connect to the RTMP port with curl
  const curl = spawn('curl', [
    '-v',
    '--connect-timeout', '10',
    '--max-time', '15',
    'rtmp://live.twitch.tv:1935/app'
  ]);
  
  curl.stderr.on('data', (data) => {
    const output = data.toString();
    console.log('🔍 Curl output:', output.trim());
  });
  
  curl.on('close', (code) => {
    console.log(`\n📋 Curl test completed (code: ${code})`);
    
    console.log('\n🔧 Troubleshooting Summary:');
    console.log('1. ✅ Basic TCP connectivity to Twitch works');
    console.log('2. ❌ RTMP handshake consistently fails');
    console.log('3. ❌ Multiple FFmpeg configurations failed');
    console.log('4. ❌ Both regular RTMP and RTMPS failed');
    console.log('');
    console.log('💡 Possible causes:');
    console.log('   • Corporate firewall blocking RTMP protocol');
    console.log('   • ISP blocking RTMP traffic');
    console.log('   • Twitch requiring specific RTMP client identification');
    console.log('   • FFmpeg version incompatibility with Twitch');
    console.log('   • Account-level streaming restrictions');
    console.log('');
    console.log('🧪 Recommended next steps:');
    console.log('   1. Test with OBS Studio to verify stream key works');
    console.log('   2. Try from a different network (mobile hotspot)');
    console.log('   3. Check if corporate/ISP firewall blocks RTMP');
    console.log('   4. Try an older FFmpeg version (4.x or 5.x)');
  });
}

// Start the deep debugging
testWithMaxDebug();
