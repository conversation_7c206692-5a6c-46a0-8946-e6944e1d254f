#!/usr/bin/env node

const { spawn } = require('child_process');

console.log('🧪 Testing FFmpeg with generated YUV420p frames...\n');

const STREAM_KEY = 'live_1048713352_TbNczEfLtlt6wRdvxXLBrfqBBQ72Ax';

// Create YUV420p test frame
function createTestFrame(width, height, frameCounter) {
  const ySize = width * height;
  const uvSize = (width * height) / 4;
  const frameSize = ySize + uvSize + uvSize;
  const testFrame = Buffer.alloc(frameSize);

  const time = frameCounter / 30.0;

  // Fill Y (luminance) component
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      const index = y * width + x;
      let intensity = ((x + y) / (width + height)) * 255;
      intensity += Math.sin((x / 100) + time * 2) * 30;
      intensity += Math.cos((y / 80) + time * 1.5) * 20;
      testFrame[index] = Math.max(0, Math.min(255, Math.round(intensity)));
    }
  }

  // Fill U and V components
  const uStart = ySize;
  const vStart = ySize + uvSize;
  
  for (let i = 0; i < uvSize; i++) {
    const u = 128 + Math.sin(time * 2 + i * 0.001) * 60;
    const v = 128 + Math.cos(time * 1.5 + i * 0.001) * 60;
    
    testFrame[uStart + i] = Math.max(0, Math.min(255, Math.round(u)));
    testFrame[vStart + i] = Math.max(0, Math.min(255, Math.round(v)));
  }

  return testFrame;
}

// Test FFmpeg command
const ffmpegArgs = [
  '-v', 'info',
  '-stats',
  '-f', 'rawvideo',
  '-pixel_format', 'yuv420p',
  '-video_size', '1920x1080',
  '-framerate', '30',
  '-i', 'pipe:0',
  
  // Generate silent audio
  '-f', 'lavfi',
  '-i', 'anullsrc=channel_layout=stereo:sample_rate=48000',

  // Video encoding
  '-c:v', 'libx264',
  '-preset', 'ultrafast',
  '-tune', 'zerolatency',
  '-profile:v', 'baseline',
  '-level', '3.1',
  '-pix_fmt', 'yuv420p',
  '-g', '60',
  '-keyint_min', '60',
  '-sc_threshold', '0',
  '-b:v', '2500k',
  '-maxrate', '3000k',
  '-bufsize', '6000k',
  
  // Audio encoding
  '-c:a', 'aac',
  '-b:a', '128k',
  '-ar', '44100',
  '-ac', '2',
  
  // Output
  '-f', 'flv',
  '-rtmp_live', 'live',
  `rtmp://live.twitch.tv/app/${STREAM_KEY}`
];

console.log('FFmpeg command:');
console.log(`ffmpeg ${ffmpegArgs.join(' ')}\n`);

const ffmpeg = spawn('ffmpeg', ffmpegArgs, {
  stdio: ['pipe', 'pipe', 'pipe']
});

let frameCounter = 0;
let streamingStarted = false;

// Send frames
const frameInterval = setInterval(() => {
  if (ffmpeg.stdin && !ffmpeg.stdin.destroyed) {
    try {
      const frame = createTestFrame(1920, 1080, frameCounter);
      ffmpeg.stdin.write(frame);
      frameCounter++;
      
      if (frameCounter % 30 === 0) {
        console.log(`📊 Sent ${frameCounter} frames`);
      }
    } catch (error) {
      console.error(`❌ Error writing frame:`, error);
      clearInterval(frameInterval);
    }
  } else {
    clearInterval(frameInterval);
  }
}, 33); // ~30 FPS

ffmpeg.stderr.on('data', (data) => {
  const output = data.toString();
  
  // Log all output for debugging
  console.log('🔍 FFmpeg:', output.trim());
  
  if (output.includes('fps=') && !streamingStarted) {
    streamingStarted = true;
    console.log('✅ Stream started successfully!');
    console.log('🎉 Check your Twitch stream!');
  }
});

ffmpeg.on('close', (code) => {
  clearInterval(frameInterval);
  console.log(`\n🏁 FFmpeg exited with code: ${code}`);
  
  if (code === 0) {
    console.log('✅ Stream completed successfully');
  } else {
    console.log('❌ Stream failed');
  }
});

ffmpeg.on('error', (error) => {
  clearInterval(frameInterval);
  console.error('❌ FFmpeg error:', error);
});

// Run for 30 seconds
setTimeout(() => {
  console.log('\n🛑 Stopping test...');
  ffmpeg.kill('SIGTERM');
}, 30000);

// Handle Ctrl+C
process.on('SIGINT', () => {
  console.log('\n🛑 Test interrupted');
  clearInterval(frameInterval);
  ffmpeg.kill('SIGTERM');
  process.exit(0);
});
