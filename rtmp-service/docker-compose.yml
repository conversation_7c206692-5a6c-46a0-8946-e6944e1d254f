version: '3.8'

services:
  rtmp-service:
    build: .
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=development
      - PORT=3002
      - TURN_SERVER=${TURN_SERVER:-}
      - TURN_USERNAME=${TURN_USERNAME:-webrtc}
      - TURN_PASSWORD=${TURN_PASSWORD:-webrtc123}
    volumes:
      - ./logs:/usr/src/app/logs
      - ./temp:/usr/src/app/temp
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3002/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - rtmp-network

networks:
  rtmp-network:
    driver: bridge
