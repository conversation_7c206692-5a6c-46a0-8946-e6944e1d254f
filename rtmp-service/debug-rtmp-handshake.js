#!/usr/bin/env node

const { spawn } = require('child_process');

console.log('🔍 Debugging RTMP Handshake Issues...\n');

const STREAM_KEY = 'live_1048713352_TbNczEfLtlt6wRdvxXLBrfqBBQ72Ax';

// Test different RTMP parameter combinations
const testConfigurations = [
  {
    name: 'Standard Configuration',
    args: [
      '-f', 'lavfi', '-i', 'testsrc=duration=5:size=1920x1080:rate=30',
      '-f', 'lavfi', '-i', 'sine=frequency=1000:duration=5',
      '-c:v', 'libx264', '-preset', 'ultrafast', '-tune', 'zerolatency',
      '-profile:v', 'main', '-level', '4.0', '-pix_fmt', 'yuv420p',
      '-g', '60', '-keyint_min', '60', '-sc_threshold', '0',
      '-b:v', '1000k', '-maxrate', '1500k', '-bufsize', '3000k',
      '-c:a', 'aac', '-b:a', '128k', '-ar', '44100', '-ac', '2',
      '-f', 'flv', '-rtmp_live', 'live'
    ]
  },
  {
    name: 'Without rtmp_live parameter',
    args: [
      '-f', 'lavfi', '-i', 'testsrc=duration=5:size=1920x1080:rate=30',
      '-f', 'lavfi', '-i', 'sine=frequency=1000:duration=5',
      '-c:v', 'libx264', '-preset', 'ultrafast', '-tune', 'zerolatency',
      '-profile:v', 'main', '-level', '4.0', '-pix_fmt', 'yuv420p',
      '-g', '60', '-keyint_min', '60', '-sc_threshold', '0',
      '-b:v', '1000k', '-maxrate', '1500k', '-bufsize', '3000k',
      '-c:a', 'aac', '-b:a', '128k', '-ar', '44100', '-ac', '2',
      '-f', 'flv'
    ]
  },
  {
    name: 'With rtmp_playpath',
    args: [
      '-f', 'lavfi', '-i', 'testsrc=duration=5:size=1920x1080:rate=30',
      '-f', 'lavfi', '-i', 'sine=frequency=1000:duration=5',
      '-c:v', 'libx264', '-preset', 'ultrafast', '-tune', 'zerolatency',
      '-profile:v', 'main', '-level', '4.0', '-pix_fmt', 'yuv420p',
      '-g', '60', '-keyint_min', '60', '-sc_threshold', '0',
      '-b:v', '1000k', '-maxrate', '1500k', '-bufsize', '3000k',
      '-c:a', 'aac', '-b:a', '128k', '-ar', '44100', '-ac', '2',
      '-f', 'flv', '-rtmp_playpath', STREAM_KEY
    ]
  },
  {
    name: 'Baseline profile',
    args: [
      '-f', 'lavfi', '-i', 'testsrc=duration=5:size=1920x1080:rate=30',
      '-f', 'lavfi', '-i', 'sine=frequency=1000:duration=5',
      '-c:v', 'libx264', '-preset', 'ultrafast', '-tune', 'zerolatency',
      '-profile:v', 'baseline', '-level', '3.1', '-pix_fmt', 'yuv420p',
      '-g', '60', '-keyint_min', '60', '-sc_threshold', '0',
      '-b:v', '1000k', '-maxrate', '1500k', '-bufsize', '3000k',
      '-c:a', 'aac', '-b:a', '128k', '-ar', '44100', '-ac', '2',
      '-f', 'flv', '-rtmp_live', 'live'
    ]
  },
  {
    name: 'Lower resolution',
    args: [
      '-f', 'lavfi', '-i', 'testsrc=duration=5:size=1280x720:rate=30',
      '-f', 'lavfi', '-i', 'sine=frequency=1000:duration=5',
      '-c:v', 'libx264', '-preset', 'ultrafast', '-tune', 'zerolatency',
      '-profile:v', 'main', '-level', '3.1', '-pix_fmt', 'yuv420p',
      '-g', '60', '-keyint_min', '60', '-sc_threshold', '0',
      '-b:v', '800k', '-maxrate', '1000k', '-bufsize', '2000k',
      '-c:a', 'aac', '-b:a', '128k', '-ar', '44100', '-ac', '2',
      '-f', 'flv', '-rtmp_live', 'live'
    ]
  }
];

let currentTest = 0;

function runNextTest() {
  if (currentTest >= testConfigurations.length) {
    console.log('\n❌ All configurations failed!');
    console.log('🔍 This suggests a deeper issue with:');
    console.log('   1. Twitch RTMP server compatibility');
    console.log('   2. Network/firewall blocking RTMP traffic');
    console.log('   3. FFmpeg version compatibility');
    console.log('   4. Account-level streaming restrictions');
    console.log('');
    console.log('💡 Try testing with OBS Studio to verify the stream key works');
    return;
  }

  const config = testConfigurations[currentTest];
  console.log(`🧪 Test ${currentTest + 1}/${testConfigurations.length}: ${config.name}`);
  
  // Test both endpoints for each configuration
  testEndpoint('rtmp://live.twitch.tv/app', config, () => {
    testEndpoint('rtmp://live.twitch.tv/live', config, () => {
      currentTest++;
      setTimeout(runNextTest, 1000);
    });
  });
}

function testEndpoint(rtmpUrl, config, callback) {
  const fullUrl = `${rtmpUrl}/${STREAM_KEY}`;
  const args = ['-v', 'error', '-y', ...config.args, fullUrl];
  
  console.log(`   Testing: ${rtmpUrl.split('/').pop()}`);
  
  const ffmpeg = spawn('ffmpeg', args);
  let success = false;
  let errorDetails = '';
  
  const timeout = setTimeout(() => {
    if (!ffmpeg.killed) {
      ffmpeg.kill('SIGTERM');
      if (!success) {
        console.log(`   ❌ Timeout`);
      }
      callback();
    }
  }, 8000);
  
  ffmpeg.stderr.on('data', (data) => {
    const output = data.toString();
    errorDetails += output;
    
    if (output.includes('fps=') && !success) {
      success = true;
      console.log(`   ✅ SUCCESS! Stream started`);
      console.log(`🎉 Working configuration found: ${config.name} with ${rtmpUrl}`);
      console.log('');
      console.log('📋 Working FFmpeg command:');
      console.log(`ffmpeg ${args.join(' ')}`);
      clearTimeout(timeout);
      ffmpeg.kill('SIGTERM');
      process.exit(0);
    }
  });
  
  ffmpeg.on('close', (code) => {
    clearTimeout(timeout);
    if (!success) {
      if (errorDetails.includes('Input/output error')) {
        console.log(`   ❌ RTMP handshake failed`);
      } else if (errorDetails.includes('Connection refused')) {
        console.log(`   ❌ Connection refused`);
      } else {
        console.log(`   ❌ Failed (code: ${code})`);
      }
    }
    callback();
  });
  
  ffmpeg.on('error', (error) => {
    clearTimeout(timeout);
    console.log(`   ❌ Error: ${error.message}`);
    callback();
  });
}

// Start testing
console.log('🔗 First, testing basic connectivity...');
const nc = spawn('nc', ['-v', '-w', '3', 'live.twitch.tv', '1935']);

nc.on('close', (code) => {
  if (code === 0) {
    console.log('✅ Basic connectivity successful\n');
    runNextTest();
  } else {
    console.error('❌ Cannot connect to live.twitch.tv:1935');
    console.error('💡 Network connectivity issue - check firewall/internet');
    process.exit(1);
  }
});

nc.stderr.on('data', (data) => {
  // nc outputs connection info to stderr
});

nc.on('error', (error) => {
  console.error('❌ Error testing connectivity:', error.message);
  process.exit(1);
});
