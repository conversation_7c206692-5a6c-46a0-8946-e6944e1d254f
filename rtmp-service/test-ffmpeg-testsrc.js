#!/usr/bin/env node

const { spawn } = require('child_process');

console.log('🧪 Testing FFmpeg with internal test source...\n');

const STREAM_KEY = 'live_1048713352_TbNczEfLtlt6wRdvxXLBrfqBBQ72Ax';

// Test with FFmpeg's internal test source (same as working test)
const ffmpegArgs = [
  '-v', 'info',
  '-stats',
  
  // Use FFmpeg's internal test source
  '-f', 'lavfi',
  '-i', 'testsrc=duration=30:size=1920x1080:rate=30',
  '-f', 'lavfi',
  '-i', 'anullsrc=channel_layout=stereo:sample_rate=48000',

  // Video encoding - SAME as our service
  '-c:v', 'libx264',
  '-preset', 'ultrafast',
  '-tune', 'zerolatency',
  '-profile:v', 'baseline',
  '-level', '3.1',
  '-pix_fmt', 'yuv420p',
  '-g', '60',
  '-keyint_min', '60',
  '-sc_threshold', '0',
  '-b:v', '2500k',
  '-maxrate', '3000k',
  '-bufsize', '6000k',
  
  // Audio encoding - SAME as our service
  '-c:a', 'aac',
  '-b:a', '128k',
  '-ar', '44100',
  '-ac', '2',
  
  // Output - SAME as our service
  '-f', 'flv',
  '-rtmp_live', 'live',
  `rtmp://live.twitch.tv/app/${STREAM_KEY}`
];

console.log('FFmpeg command:');
console.log(`ffmpeg ${ffmpegArgs.join(' ')}\n`);

const ffmpeg = spawn('ffmpeg', ffmpegArgs);

let streamingStarted = false;

ffmpeg.stderr.on('data', (data) => {
  const output = data.toString();
  
  // Log all output for debugging
  console.log('🔍 FFmpeg:', output.trim());
  
  if (output.includes('fps=') && !streamingStarted) {
    streamingStarted = true;
    console.log('✅ Stream started successfully!');
    console.log('🎉 Check your Twitch stream!');
  }
  
  if (output.includes('Cannot open connection')) {
    console.log('❌ RTMP connection failed - this confirms the issue');
  }
});

ffmpeg.on('close', (code) => {
  console.log(`\n🏁 FFmpeg exited with code: ${code}`);
  
  if (code === 0) {
    console.log('✅ Stream completed successfully');
  } else {
    console.log('❌ Stream failed');
  }
});

ffmpeg.on('error', (error) => {
  console.error('❌ FFmpeg error:', error);
});

// Handle Ctrl+C
process.on('SIGINT', () => {
  console.log('\n🛑 Test interrupted');
  ffmpeg.kill('SIGTERM');
  process.exit(0);
});
