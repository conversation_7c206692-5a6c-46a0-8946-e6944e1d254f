const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const { v4: uuidv4 } = require('uuid');
const { WebRTCToRTMPBridge } = require('./webrtc-rtmp-bridge');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: true,
    methods: ["GET", "POST"],
    credentials: true
  },
  transports: ['websocket', 'polling']
});

app.use(cors({
  origin: true,
  credentials: true
}));
app.use(express.json());

// Store active streaming sessions
const streamingSessions = new Map();

// Socket.io connection handling
io.on('connection', (socket) => {
  console.log(`🔌 Client connected: ${socket.id}`);

  // Handle stream start request
  socket.on('start-stream', async ({ rtmpUrl, streamKey, roomId }) => {
    console.log(`🚀 Start stream request from ${socket.id} for room ${roomId}`);

    try {
      const sessionId = uuidv4();
      const bridge = new WebRTCToRTMPBridge(sessionId, rtmpUrl, streamKey);
      streamingSessions.set(sessionId, bridge);

      // Create WebRTC peer connection
      await bridge.createPeerConnection();

      // Send session info back to client
      socket.emit('stream-session-created', {
        sessionId: sessionId,
        success: true
      });

      console.log(`✅ Stream session created: ${sessionId}`);

    } catch (error) {
      console.error(`❌ Error creating stream session:`, error);
      socket.emit('stream-session-created', {
        success: false,
        error: error.message
      });
    }
  });

  // Handle WebRTC offer
  socket.on('webrtc-offer', async ({ sessionId, offer }) => {
    console.log(`📨 Received WebRTC offer for session ${sessionId}`);

    const bridge = streamingSessions.get(sessionId);
    if (!bridge) {
      socket.emit('webrtc-error', { sessionId, error: 'Session not found' });
      return;
    }

    try {
      const answer = await bridge.handleOffer(offer);
      socket.emit('webrtc-answer', { sessionId, answer });
    } catch (error) {
      console.error(`❌ Error handling offer for session ${sessionId}:`, error);
      socket.emit('webrtc-error', { sessionId, error: error.message });
    }
  });

  // Handle ICE candidates
  socket.on('webrtc-ice-candidate', async ({ sessionId, candidate }) => {
    const bridge = streamingSessions.get(sessionId);
    if (bridge) {
      await bridge.handleIceCandidate(candidate);
    }
  });

  // Handle stream stop
  socket.on('stop-stream', ({ sessionId }) => {
    console.log(`🛑 Stop stream request for session ${sessionId}`);

    const bridge = streamingSessions.get(sessionId);
    if (bridge) {
      bridge.stop();
      streamingSessions.delete(sessionId);
      socket.emit('stream-stopped', { sessionId, success: true });
    } else {
      socket.emit('stream-stopped', { sessionId, success: false, error: 'Session not found' });
    }
  });

  socket.on('disconnect', () => {
    console.log(`🔌 Client disconnected: ${socket.id}`);
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  const activeSessions = streamingSessions.size;
  res.json({
    status: 'healthy',
    uptime: process.uptime(),
    activeSessions: activeSessions,
    timestamp: new Date().toISOString()
  });
});

// Sessions API endpoint
app.get('/sessions', (req, res) => {
  const sessions = Array.from(streamingSessions.values()).map(bridge => bridge.getStatus());
  res.json({
    activeSessions: sessions.length,
    sessions: sessions
  });
});

// Get specific session
app.get('/sessions/:id', (req, res) => {
  const bridge = streamingSessions.get(req.params.id);
  if (bridge) {
    res.json(bridge.getStatus());
  } else {
    res.status(404).json({ error: 'Session not found' });
  }
});

// Start server
const PORT = process.env.PORT || 3002;
server.listen(PORT, () => {
  console.log(`🚀 RTMP Streaming Service running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`📋 Sessions API: http://localhost:${PORT}/sessions`);
});
