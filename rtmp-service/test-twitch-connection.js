#!/usr/bin/env node

const { spawn } = require('child_process');

console.log('🧪 Testing Twitch RTMP Connection...\n');

// The stream key from your error logs
const STREAM_KEY = 'live_1048713352_TbNczEfLtlt6wRdvxXLBrfqBBQ72Ax';
const RTMP_URL = 'rtmp://live.twitch.tv/app';

console.log('📋 Current Configuration:');
console.log(`   RTMP URL: ${RTMP_URL}`);
console.log(`   Stream Key: ${STREAM_KEY}`);
console.log('');

// Test 1: Basic connectivity
console.log('🔗 Test 1: Testing basic connectivity to Twitch RTMP server...');
const nc = spawn('nc', ['-v', '-w', '5', 'live.twitch.tv', '1935']);

nc.on('close', (code) => {
  if (code === 0) {
    console.log('✅ Basic connectivity to live.twitch.tv:1935 successful\n');
    
    // Test 2: FFmpeg RTMP connection
    console.log('🎬 Test 2: Testing FFmpeg RTMP connection with test video...');
    console.log('⏱️  This will run for 10 seconds...\n');
    
    const ffmpegArgs = [
      '-f', 'lavfi',
      '-i', 'testsrc=duration=10:size=1920x1080:rate=30',
      '-f', 'lavfi', 
      '-i', 'sine=frequency=1000:duration=10',
      '-c:v', 'libx264',
      '-preset', 'ultrafast',
      '-tune', 'zerolatency',
      '-profile:v', 'baseline',
      '-level', '3.1',
      '-pix_fmt', 'yuv420p',
      '-g', '60',
      '-keyint_min', '60',
      '-sc_threshold', '0',
      '-b:v', '2500k',
      '-maxrate', '3000k',
      '-bufsize', '6000k',
      '-c:a', 'aac',
      '-b:a', '128k',
      '-ar', '44100',
      '-ac', '2',
      '-f', 'flv',
      '-rtmp_live', 'live',
      `${RTMP_URL}/${STREAM_KEY}`
    ];
    
    console.log('FFmpeg command:');
    console.log(`ffmpeg ${ffmpegArgs.join(' ')}\n`);
    
    const ffmpeg = spawn('ffmpeg', ffmpegArgs);
    
    ffmpeg.stderr.on('data', (data) => {
      const output = data.toString();
      
      // Look for specific error patterns
      if (output.includes('Error opening output')) {
        console.error('❌ RTMP Connection Failed!');
        console.error('📋 Error details:', output.trim());
        console.error('');
        console.error('🔧 Possible solutions:');
        console.error('   1. Stream key is expired or invalid');
        console.error('   2. Twitch account not set to "Go Live"');
        console.error('   3. Stream key was regenerated');
        console.error('   4. Account may be suspended or restricted');
        console.error('');
        console.error('📝 Steps to fix:');
        console.error('   1. Go to https://dashboard.twitch.tv/');
        console.error('   2. Navigate to Settings > Stream');
        console.error('   3. Copy the Primary Stream Key');
        console.error('   4. Make sure "Go Live" is enabled');
        console.error('   5. Update your application with the new key');
      } else if (output.includes('Stream mapping')) {
        console.log('✅ FFmpeg started successfully');
      } else if (output.includes('fps=')) {
        console.log('📊 Streaming:', output.trim());
      } else if (output.includes('Connection')) {
        console.log('🔗 Connection info:', output.trim());
      }
    });
    
    ffmpeg.on('close', (code) => {
      console.log('');
      if (code === 0) {
        console.log('✅ Test completed successfully!');
        console.log('🎉 Your Twitch RTMP connection is working!');
        console.log('💡 The WebRTC-to-RTMP service should work with this configuration.');
      } else {
        console.log(`❌ FFmpeg exited with code ${code}`);
        console.log('💡 This indicates an RTMP connection problem.');
        console.log('');
        console.log('🔧 Next steps:');
        console.log('   1. Check your Twitch stream key');
        console.log('   2. Ensure your account can stream');
        console.log('   3. Try regenerating the stream key');
        console.log('   4. Verify account is not restricted');
      }
    });
    
    // Handle Ctrl+C
    process.on('SIGINT', () => {
      console.log('\n🛑 Test interrupted by user');
      ffmpeg.kill('SIGTERM');
      process.exit(0);
    });
    
  } else {
    console.error('❌ Cannot connect to live.twitch.tv:1935');
    console.error('💡 This indicates a network connectivity issue.');
    console.error('🔧 Check your internet connection and firewall settings.');
  }
});

nc.stderr.on('data', (data) => {
  const output = data.toString();
  if (output.includes('succeeded')) {
    // nc success message goes to stderr
    console.log('✅ Connection successful');
  } else {
    console.error('Connection details:', output.trim());
  }
});

nc.on('error', (error) => {
  console.error('❌ Error running nc command:', error.message);
  console.error('💡 Make sure netcat (nc) is installed on your system');
  
  if (process.platform === 'darwin') {
    console.error('   Install with: brew install netcat');
  } else if (process.platform === 'linux') {
    console.error('   Install with: sudo apt-get install netcat');
  }
});
