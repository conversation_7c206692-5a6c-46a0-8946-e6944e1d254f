#!/usr/bin/env node

const { spawn } = require('child_process');

console.log('🧪 Testing Twitch RTMP Connection...\n');

// The stream key from your error logs
const STREAM_KEY = 'live_1048713352_TbNczEfLtlt6wRdvxXLBrfqBBQ72Ax';
const RTMP_URL_APP = 'rtmp://live.twitch.tv/app';
const RTMP_URL_LIVE = 'rtmp://live.twitch.tv/live';

console.log('📋 Testing both RTMP URL formats:');
console.log(`   Format 1: ${RTMP_URL_APP}`);
console.log(`   Format 2: ${RTMP_URL_LIVE}`);
console.log(`   Stream Key: ${STREAM_KEY}`);
console.log('');

function testRTMPEndpoint(rtmpUrl, endpointName) {
  console.log(`🎬 Testing FFmpeg RTMP connection with ${endpointName}...`);
  console.log('⏱️  This will run for 10 seconds...\n');

  const ffmpegArgs = [
      '-v', 'verbose',  // Add verbose logging
      '-f', 'lavfi',
      '-i', 'testsrc=duration=10:size=1920x1080:rate=30',
      '-f', 'lavfi',
      '-i', 'sine=frequency=1000:duration=10',
      '-c:v', 'libx264',
      '-preset', 'ultrafast',
      '-tune', 'zerolatency',
      '-profile:v', 'main',  // Changed from baseline to main
      '-level', '4.0',       // Changed from 3.1 to 4.0
      '-pix_fmt', 'yuv420p',
      '-g', '60',
      '-keyint_min', '60',
      '-sc_threshold', '0',
      '-b:v', '1000k',       // Reduced bitrate
      '-maxrate', '1500k',   // Reduced max rate
      '-bufsize', '3000k',   // Reduced buffer size
      '-c:a', 'aac',
      '-b:a', '128k',
      '-ar', '44100',
      '-ac', '2',
      '-f', 'flv',
      '-rtmp_live', 'live',
      '-rtmp_conn', 'S:live',  // Add explicit live connection
      `${RTMP_URL}/${STREAM_KEY}`
    ];
    
    console.log('FFmpeg command:');
    console.log(`ffmpeg ${ffmpegArgs.join(' ')}\n`);
    
    const ffmpeg = spawn('ffmpeg', ffmpegArgs);
    
    ffmpeg.stderr.on('data', (data) => {
      const output = data.toString();

      // Log all output for debugging
      console.log('🔍 FFmpeg output:', output.trim());

      // Look for specific error patterns
      if (output.includes('Error opening output')) {
        console.error('❌ RTMP Connection Failed!');
        console.error('📋 Error details:', output.trim());
        console.error('');
        console.error('🔧 Possible solutions:');
        console.error('   1. Stream key is expired or invalid');
        console.error('   2. Twitch account not set to "Go Live"');
        console.error('   3. Stream key was regenerated');
        console.error('   4. Account may be suspended or restricted');
        console.error('   5. Try using rtmp://live.twitch.tv/live instead of /app');
        console.error('');
        console.error('📝 Steps to fix:');
        console.error('   1. Go to https://dashboard.twitch.tv/');
        console.error('   2. Navigate to Settings > Stream');
        console.error('   3. Copy the Primary Stream Key');
        console.error('   4. Make sure "Go Live" is enabled');
        console.error('   5. Update your application with the new key');
      } else if (output.includes('Stream mapping')) {
        console.log('✅ FFmpeg started successfully');
      } else if (output.includes('fps=')) {
        console.log('📊 Streaming:', output.trim());
      } else if (output.includes('Connection') || output.includes('RTMP')) {
        console.log('🔗 Connection info:', output.trim());
      }
    });
    
    ffmpeg.on('close', (code) => {
      console.log('');
      if (code === 0) {
        console.log('✅ Test completed successfully!');
        console.log('🎉 Your Twitch RTMP connection is working!');
        console.log('💡 The WebRTC-to-RTMP service should work with this configuration.');
      } else {
        console.log(`❌ FFmpeg exited with code ${code}`);
        console.log('💡 This indicates an RTMP connection problem.');
        console.log('');
        console.log('🔧 Next steps:');
        console.log('   1. Check your Twitch stream key');
        console.log('   2. Ensure your account can stream');
        console.log('   3. Try regenerating the stream key');
        console.log('   4. Verify account is not restricted');
      }
    });
    
    // Handle Ctrl+C
    process.on('SIGINT', () => {
      console.log('\n🛑 Test interrupted by user');
      ffmpeg.kill('SIGTERM');
      process.exit(0);
    });
    
  } else {
    console.error('❌ Cannot connect to live.twitch.tv:1935');
    console.error('💡 This indicates a network connectivity issue.');
    console.error('🔧 Check your internet connection and firewall settings.');
  }
});

nc.stderr.on('data', (data) => {
  const output = data.toString();
  if (output.includes('succeeded')) {
    // nc success message goes to stderr
    console.log('✅ Connection successful');
  } else {
    console.error('Connection details:', output.trim());
  }
});

nc.on('error', (error) => {
  console.error('❌ Error running nc command:', error.message);
  console.error('💡 Make sure netcat (nc) is installed on your system');
  
  if (process.platform === 'darwin') {
    console.error('   Install with: brew install netcat');
  } else if (process.platform === 'linux') {
    console.error('   Install with: sudo apt-get install netcat');
  }
});
