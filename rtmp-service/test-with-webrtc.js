#!/usr/bin/env node

const io = require('socket.io-client');
const wrtc = require('wrtc');

console.log('🧪 Testing RTMP streaming service with real WebRTC...');

// Connect to RTMP service
const socket = io('http://localhost:3002', {
  transports: ['websocket', 'polling']
});

let sessionId = null;

socket.on('connect', () => {
  console.log('✅ Connected to RTMP service');
  console.log(`🆔 Socket ID: ${socket.id}`);
  
  // Start streaming
  console.log('🚀 Starting stream...');
  socket.emit('start-stream', {
    rtmpUrl: 'rtmp://live.twitch.tv/app',
    streamKey: 'live_1048713352_TbNczEfLtlt6wRdvxXLBrfqBBQ72Ax',
    roomId: 'test-room'
  });
});

socket.on('stream-session-created', async ({ sessionId: id, success, error }) => {
  if (success) {
    sessionId = id;
    console.log(`✅ Stream session created: ${sessionId}`);
    
    // Create a WebRTC peer connection and add a video track
    console.log('🎥 Creating WebRTC peer connection with video track...');
    
    try {
      const pc = new wrtc.RTCPeerConnection({
        iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
      });

      // Create a fake video track (this will trigger FFmpeg to start)
      const canvas = new wrtc.RTCVideoSource();
      const track = canvas.createTrack();
      
      console.log('📹 Created video track');
      
      // Add track to peer connection
      pc.addTrack(track);
      console.log('➕ Added video track to peer connection');

      // Create offer
      const offer = await pc.createOffer();
      await pc.setLocalDescription(offer);
      
      console.log('📤 Created WebRTC offer');
      
      // Send offer to server
      socket.emit('webrtc-offer', {
        sessionId: sessionId,
        offer: offer
      });
      
    } catch (error) {
      console.error('❌ Error setting up WebRTC:', error);
    }
    
  } else {
    console.error(`❌ Failed to create stream session: ${error}`);
    process.exit(1);
  }
});

socket.on('webrtc-answer', async (data) => {
  console.log('📥 Received WebRTC answer');
  console.log('🎬 FFmpeg should now start with test pattern!');
  console.log('📺 Check your Twitch stream!');
  
  // Keep running for a while
  setTimeout(() => {
    console.log('🛑 Stopping test...');
    if (sessionId) {
      socket.emit('stop-stream', { sessionId });
    }
  }, 30000); // Run for 30 seconds
});

socket.on('stream-stopped', ({ sessionId, success, error }) => {
  if (success) {
    console.log('✅ Stream stopped successfully');
  } else {
    console.error(`❌ Error stopping stream: ${error}`);
  }
  process.exit(0);
});

socket.on('connect_error', (error) => {
  console.error('❌ Connection error:', error);
  process.exit(1);
});

socket.on('disconnect', (reason) => {
  console.log(`🔌 Disconnected: ${reason}`);
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Stopping test...');
  if (sessionId) {
    socket.emit('stop-stream', { sessionId });
  }
  socket.disconnect();
  process.exit(0);
});
