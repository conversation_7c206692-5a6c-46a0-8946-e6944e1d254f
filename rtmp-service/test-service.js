#!/usr/bin/env node

/**
 * Test script for RTMP streaming service
 * Tests basic functionality without requiring full WebRTC setup
 */

const { io } = require('socket.io-client');

const SERVICE_URL = process.env.RTMP_SERVICE_URL || 'http://localhost:3002';
const TEST_RTMP_URL = 'rtmp://test.example.com/live';
const TEST_STREAM_KEY = 'test-key-123';

console.log('🧪 RTMP Service Test Suite');
console.log('==========================');
console.log(`Service URL: ${SERVICE_URL}`);
console.log('');

async function testHealthEndpoint() {
  console.log('📊 Testing health endpoint...');
  
  try {
    const response = await fetch(`${SERVICE_URL}/health`);
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Health check passed');
      console.log(`   Status: ${data.status}`);
      console.log(`   Uptime: ${Math.round(data.uptime)}s`);
      console.log(`   Active sessions: ${data.activeSessions}`);
      return true;
    } else {
      console.log('❌ Health check failed');
      return false;
    }
  } catch (error) {
    console.log(`❌ Health check error: ${error.message}`);
    return false;
  }
}

async function testSocketConnection() {
  console.log('🔌 Testing Socket.io connection...');
  
  return new Promise((resolve) => {
    const socket = io(SERVICE_URL, {
      transports: ['websocket', 'polling'],
      timeout: 5000
    });

    const timeout = setTimeout(() => {
      console.log('❌ Socket connection timeout');
      socket.disconnect();
      resolve(false);
    }, 5000);

    socket.on('connect', () => {
      clearTimeout(timeout);
      console.log('✅ Socket.io connection successful');
      socket.disconnect();
      resolve(true);
    });

    socket.on('connect_error', (error) => {
      clearTimeout(timeout);
      console.log(`❌ Socket connection error: ${error.message}`);
      resolve(false);
    });
  });
}

async function testStreamSession() {
  console.log('🎬 Testing stream session creation...');
  
  return new Promise((resolve) => {
    const socket = io(SERVICE_URL, {
      transports: ['websocket', 'polling'],
      timeout: 10000
    });

    let sessionCreated = false;

    const timeout = setTimeout(() => {
      if (!sessionCreated) {
        console.log('❌ Stream session creation timeout');
        socket.disconnect();
        resolve(false);
      }
    }, 10000);

    socket.on('connect', () => {
      console.log('   Connected to service');
      
      // Request stream session
      socket.emit('start-stream', {
        rtmpUrl: TEST_RTMP_URL,
        streamKey: TEST_STREAM_KEY,
        roomId: 'test-room'
      });
    });

    socket.on('stream-session-created', ({ sessionId, success, error }) => {
      clearTimeout(timeout);
      sessionCreated = true;
      
      if (success) {
        console.log('✅ Stream session created successfully');
        console.log(`   Session ID: ${sessionId}`);
        
        // Clean up - stop the session
        socket.emit('stop-stream', { sessionId });
        
        socket.on('stream-stopped', () => {
          console.log('✅ Stream session stopped successfully');
          socket.disconnect();
          resolve(true);
        });
      } else {
        console.log(`❌ Stream session creation failed: ${error}`);
        socket.disconnect();
        resolve(false);
      }
    });

    socket.on('connect_error', (error) => {
      clearTimeout(timeout);
      console.log(`❌ Socket connection error: ${error.message}`);
      resolve(false);
    });
  });
}

async function testSessionsAPI() {
  console.log('📋 Testing sessions API...');
  
  try {
    const response = await fetch(`${SERVICE_URL}/sessions`);
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Sessions API working');
      console.log(`   Active sessions: ${data.activeSessions}`);
      return true;
    } else {
      console.log('❌ Sessions API failed');
      return false;
    }
  } catch (error) {
    console.log(`❌ Sessions API error: ${error.message}`);
    return false;
  }
}

async function runTests() {
  console.log('Starting tests...\n');
  
  const results = {
    health: await testHealthEndpoint(),
    socket: await testSocketConnection(),
    session: await testStreamSession(),
    api: await testSessionsAPI()
  };
  
  console.log('\n📊 Test Results:');
  console.log('================');
  
  const passed = Object.values(results).filter(Boolean).length;
  const total = Object.keys(results).length;
  
  Object.entries(results).forEach(([test, result]) => {
    const status = result ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${test.charAt(0).toUpperCase() + test.slice(1)} test`);
  });
  
  console.log(`\nOverall: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    console.log('🎉 All tests passed! RTMP service is working correctly.');
    process.exit(0);
  } else {
    console.log('⚠️  Some tests failed. Check the service configuration.');
    process.exit(1);
  }
}

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log('Usage: node test-service.js [options]');
  console.log('');
  console.log('Options:');
  console.log('  --help, -h     Show this help message');
  console.log('');
  console.log('Environment Variables:');
  console.log('  RTMP_SERVICE_URL   URL of the RTMP service (default: http://localhost:3002)');
  console.log('');
  console.log('Examples:');
  console.log('  node test-service.js');
  console.log('  RTMP_SERVICE_URL=http://your-service.com:3002 node test-service.js');
  process.exit(0);
}

// Run the tests
runTests().catch((error) => {
  console.error('❌ Test suite error:', error);
  process.exit(1);
});
