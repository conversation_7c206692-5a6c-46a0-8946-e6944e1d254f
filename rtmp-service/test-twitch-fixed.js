#!/usr/bin/env node

const { spawn } = require('child_process');

console.log('🧪 Testing Twitch RTMP Connection...\n');

// The stream key from your error logs
const STREAM_KEY = 'live_1048713352_TbNczEfLtlt6wRdvxXLBrfqBBQ72Ax';
const RTMP_ENDPOINTS = [
  { url: 'rtmp://live.twitch.tv/app', name: '/app endpoint' },
  { url: 'rtmp://live.twitch.tv/live', name: '/live endpoint' }
];

console.log('📋 Testing Configuration:');
console.log(`   Stream Key: ${STREAM_KEY}`);
console.log(`   Endpoints to test: ${RTMP_ENDPOINTS.length}`);
console.log('');

// Test 1: Basic connectivity
console.log('🔗 Test 1: Testing basic connectivity to Twitch RTMP server...');
const nc = spawn('nc', ['-v', '-w', '5', 'live.twitch.tv', '1935']);

nc.on('close', (code) => {
  if (code === 0) {
    console.log('✅ Basic connectivity to live.twitch.tv:1935 successful\n');
    
    // Test each endpoint
    testNextEndpoint(0);
  } else {
    console.error('❌ Cannot connect to live.twitch.tv:1935');
    console.error('💡 This indicates a network connectivity issue.');
    console.error('🔧 Check your internet connection and firewall settings.');
  }
});

nc.stderr.on('data', (data) => {
  const output = data.toString();
  if (output.includes('succeeded')) {
    console.log('✅ Connection successful');
  } else {
    console.error('Connection details:', output.trim());
  }
});

nc.on('error', (error) => {
  console.error('❌ Error running nc command:', error.message);
  console.error('💡 Make sure netcat (nc) is installed on your system');
  
  if (process.platform === 'darwin') {
    console.error('   Install with: brew install netcat');
  } else if (process.platform === 'linux') {
    console.error('   Install with: sudo apt-get install netcat');
  }
});

function testNextEndpoint(index) {
  if (index >= RTMP_ENDPOINTS.length) {
    console.log('\n❌ All RTMP endpoints failed!');
    console.log('🔧 Possible solutions:');
    console.log('   1. Stream key is expired or invalid');
    console.log('   2. Twitch account not set to "Go Live"');
    console.log('   3. Stream key was regenerated');
    console.log('   4. Account may be suspended or restricted');
    console.log('');
    console.log('📝 Steps to fix:');
    console.log('   1. Go to https://dashboard.twitch.tv/');
    console.log('   2. Navigate to Settings > Stream');
    console.log('   3. Copy the Primary Stream Key');
    console.log('   4. Make sure "Go Live" is enabled');
    console.log('   5. Update your application with the new key');
    return;
  }

  const endpoint = RTMP_ENDPOINTS[index];
  console.log(`🎬 Test ${index + 2}: Testing ${endpoint.name}...`);
  console.log('⏱️  This will run for 10 seconds...\n');
  
  const ffmpegArgs = [
    '-v', 'verbose',
    '-f', 'lavfi',
    '-i', 'testsrc=duration=10:size=1920x1080:rate=30',
    '-f', 'lavfi', 
    '-i', 'sine=frequency=1000:duration=10',
    '-c:v', 'libx264',
    '-preset', 'ultrafast',
    '-tune', 'zerolatency',
    '-profile:v', 'main',
    '-level', '4.0',
    '-pix_fmt', 'yuv420p',
    '-g', '60',
    '-keyint_min', '60',
    '-sc_threshold', '0',
    '-b:v', '1000k',
    '-maxrate', '1500k',
    '-bufsize', '3000k',
    '-c:a', 'aac',
    '-b:a', '128k',
    '-ar', '44100',
    '-ac', '2',
    '-f', 'flv',
    '-rtmp_live', 'live',
    `${endpoint.url}/${STREAM_KEY}`
  ];
  
  console.log('FFmpeg command:');
  console.log(`ffmpeg ${ffmpegArgs.join(' ')}\n`);
  
  const ffmpeg = spawn('ffmpeg', ffmpegArgs);
  
  let streamingStarted = false;
  
  ffmpeg.stderr.on('data', (data) => {
    const output = data.toString();
    
    // Log key output for debugging
    if (output.includes('Successfully connected') || 
        output.includes('fps=') || 
        output.includes('bitrate=') ||
        output.includes('Error') ||
        output.includes('Connection')) {
      console.log('🔍 FFmpeg:', output.trim());
    }
    
    if (output.includes('fps=') && !streamingStarted) {
      streamingStarted = true;
      console.log(`✅ ${endpoint.name} is working! Stream started successfully.`);
      console.log('🎉 Your Twitch RTMP connection is working!');
      console.log('💡 The WebRTC-to-RTMP service should work with this configuration.');
      
      // Kill FFmpeg and exit
      setTimeout(() => {
        ffmpeg.kill('SIGTERM');
        process.exit(0);
      }, 2000);
    }
    
    if (output.includes('Error opening output')) {
      console.error(`❌ ${endpoint.name} failed: ${output.trim()}`);
    }
  });
  
  ffmpeg.on('close', (code) => {
    if (!streamingStarted) {
      console.log(`❌ ${endpoint.name} failed (exit code: ${code})`);
      console.log('');
      
      // Try next endpoint
      setTimeout(() => testNextEndpoint(index + 1), 1000);
    }
  });
  
  // Handle Ctrl+C
  process.on('SIGINT', () => {
    console.log('\n🛑 Test interrupted by user');
    ffmpeg.kill('SIGTERM');
    process.exit(0);
  });
}
