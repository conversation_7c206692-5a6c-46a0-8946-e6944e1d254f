{"name": "webrtc-rtmp-streaming-service", "version": "1.0.0", "description": "WebRTC to RTMP streaming service using FFmpeg", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.4", "socket.io-client": "^4.7.4", "cors": "^2.8.5", "fluent-ffmpeg": "^2.1.2", "wrtc": "^0.4.7", "ws": "^8.14.2", "uuid": "^9.0.1", "@ffmpeg-installer/ffmpeg": "^1.1.0"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}}