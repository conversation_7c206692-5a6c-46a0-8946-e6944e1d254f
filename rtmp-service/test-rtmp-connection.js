#!/usr/bin/env node

/**
 * Test RTMP connection to verify credentials work
 * This sends a simple test stream to validate the RTMP endpoint
 */

const { spawn } = require('child_process');

function testRTMPConnection(rtmpUrl, streamKey) {
  return new Promise((resolve, reject) => {
    console.log('🧪 Testing RTMP connection...');
    console.log(`📡 URL: ${rtmpUrl}`);
    console.log(`🔑 Key: ${streamKey.substring(0, 8)}...${streamKey.substring(streamKey.length - 4)}`);

    const fullUrl = `${rtmpUrl}/${streamKey}`;
    
    // Create a simple test stream (5 seconds of color bars)
    const testArgs = [
      '-f', 'lavfi',
      '-i', 'testsrc2=size=1920x1080:rate=30',
      '-f', 'lavfi',
      '-i', 'sine=frequency=1000:sample_rate=44100',
      '-c:v', 'libx264',
      '-preset', 'ultrafast',
      '-tune', 'zerolatency',
      '-profile:v', 'main',
      '-level', '4.0',
      '-pix_fmt', 'yuv420p',
      '-g', '60',
      '-keyint_min', '60',
      '-b:v', '1000k',
      '-maxrate', '1500k',
      '-bufsize', '3000k',
      '-c:a', 'aac',
      '-b:a', '128k',
      '-ar', '44100',
      '-ac', '2',
      '-f', 'flv',
      '-rtmp_live', 'live',
      '-t', '5', // 5 seconds only
      fullUrl
    ];

    console.log(`🚀 Starting test stream...`);
    
    const ffmpeg = spawn('ffmpeg', testArgs, {
      stdio: ['ignore', 'pipe', 'pipe']
    });

    let hasOutput = false;
    let errorOutput = '';

    ffmpeg.stderr.on('data', (data) => {
      const message = data.toString();
      errorOutput += message;
      
      if (message.includes('fps=') || message.includes('bitrate=')) {
        hasOutput = true;
        console.log('✅ Stream is sending data successfully');
      }
      
      if (message.includes('error') || message.includes('failed')) {
        console.error(`❌ ${message.trim()}`);
      }
    });

    ffmpeg.on('exit', (code, signal) => {
      if (code === 0) {
        console.log('✅ RTMP connection test successful!');
        console.log('🎉 Your RTMP credentials are working correctly.');
        resolve(true);
      } else {
        console.error(`❌ RTMP connection test failed (exit code: ${code})`);
        
        if (errorOutput.includes('Connection refused') || 
            errorOutput.includes('Input/output error') ||
            errorOutput.includes('No such file or directory')) {
          console.error('💡 Possible issues:');
          console.error('   - Invalid stream key');
          console.error('   - RTMP server not accepting connections');
          console.error('   - Network connectivity problems');
          console.error('   - Twitch may be rejecting the stream format');
        }
        
        reject(new Error(`RTMP test failed with code ${code}`));
      }
    });

    ffmpeg.on('error', (error) => {
      console.error(`❌ FFmpeg error:`, error);
      reject(error);
    });

    // Timeout after 30 seconds
    setTimeout(() => {
      if (!ffmpeg.killed) {
        ffmpeg.kill('SIGTERM');
        reject(new Error('RTMP test timeout'));
      }
    }, 30000);
  });
}

// Command line usage
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length !== 2) {
    console.log('Usage: node test-rtmp-connection.js <rtmp-url> <stream-key>');
    console.log('Example: node test-rtmp-connection.js rtmp://live.twitch.tv/app your-stream-key');
    process.exit(1);
  }

  const [rtmpUrl, streamKey] = args;
  
  testRTMPConnection(rtmpUrl, streamKey)
    .then(() => {
      console.log('🎉 Test completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Test failed:', error.message);
      process.exit(1);
    });
}

module.exports = { testRTMPConnection };
