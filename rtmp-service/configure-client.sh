#!/bin/bash

# Configure the main application to use the deployed RTMP service
# This script gets the external IP of the deployed RTMP service and updates the configuration

set -e

# Configuration
PROJECT_ID=${GOOGLE_CLOUD_PROJECT:-"switcher-studio-233517"}
ZONE=${ZONE:-"us-central1-a"}
INSTANCE_NAME="rtmp-streaming-service"

echo "🔧 Configuring client to use deployed RTMP service..."

# Check if instance exists
if ! gcloud compute instances describe ${INSTANCE_NAME} --zone=${ZONE} --project=${PROJECT_ID} &>/dev/null; then
    echo "❌ RTMP service instance '${INSTANCE_NAME}' not found in zone ${ZONE}"
    echo "   Please deploy the RTMP service first using: ./deploy-gce.sh"
    exit 1
fi

# Get the external IP of the RTMP service
echo "📡 Getting RTMP service external IP..."
EXTERNAL_IP=$(gcloud compute instances describe ${INSTANCE_NAME} \
    --zone=${ZONE} \
    --format='get(networkInterfaces[0].accessConfigs[0].natIP)' \
    --project=${PROJECT_ID})

if [ -z "$EXTERNAL_IP" ]; then
    echo "❌ Could not get external IP for RTMP service instance"
    exit 1
fi

echo "✅ RTMP service found at: ${EXTERNAL_IP}"

# Test if the service is responding
echo "🔍 Testing RTMP service connectivity..."
if curl -f -s --connect-timeout 10 "http://${EXTERNAL_IP}:3002/health" > /dev/null; then
    echo "✅ RTMP service is responding"
else
    echo "⚠️  RTMP service is not responding yet (this is normal if just deployed)"
    echo "   The service may still be starting up. You can check status with:"
    echo "   curl http://${EXTERNAL_IP}:3002/health"
fi

# Update .env file in the root directory
ENV_FILE="../.env"
echo "📝 Updating environment configuration..."

# Create .env file if it doesn't exist
if [ ! -f "$ENV_FILE" ]; then
    touch "$ENV_FILE"
    echo "Created new .env file"
fi

# Remove existing RTMP service configuration
sed -i.bak '/^VITE_RTMP_SERVICE_URL=/d' "$ENV_FILE" 2>/dev/null || true
sed -i.bak '/^RTMP_SERVICE_URL=/d' "$ENV_FILE" 2>/dev/null || true

# Add new RTMP service configuration
echo "VITE_RTMP_SERVICE_URL=http://${EXTERNAL_IP}:3002" >> "$ENV_FILE"
echo "RTMP_SERVICE_URL=http://${EXTERNAL_IP}:3002" >> "$ENV_FILE"

echo "✅ Updated .env file with RTMP service configuration"

# Update server environment if server/.env exists
SERVER_ENV_FILE="../server/.env"
if [ -f "$SERVER_ENV_FILE" ]; then
    echo "📝 Updating server environment configuration..."
    
    # Remove existing RTMP service configuration
    sed -i.bak '/^RTMP_SERVICE_URL=/d' "$SERVER_ENV_FILE" 2>/dev/null || true
    
    # Add new RTMP service configuration
    echo "RTMP_SERVICE_URL=http://${EXTERNAL_IP}:3002" >> "$SERVER_ENV_FILE"
    
    echo "✅ Updated server .env file with RTMP service configuration"
else
    echo "📝 Creating server .env file..."
    mkdir -p ../server
    echo "RTMP_SERVICE_URL=http://${EXTERNAL_IP}:3002" > "$SERVER_ENV_FILE"
    echo "✅ Created server .env file with RTMP service configuration"
fi

echo ""
echo "🎉 Configuration complete!"
echo ""
echo "📋 RTMP Service Details:"
echo "   URL: http://${EXTERNAL_IP}:3002"
echo "   Health Check: http://${EXTERNAL_IP}:3002/health"
echo "   Sessions API: http://${EXTERNAL_IP}:3002/sessions"
echo ""
echo "📝 Environment Variables Set:"
echo "   VITE_RTMP_SERVICE_URL=http://${EXTERNAL_IP}:3002"
echo "   RTMP_SERVICE_URL=http://${EXTERNAL_IP}:3002"
echo ""
echo "🔄 Next Steps:"
echo "   1. Restart your development server: npm run dev"
echo "   2. Update your signaling server to use the RTMP service"
echo "   3. Test RTMP streaming functionality"
echo ""
echo "🔧 Useful Commands:"
echo "   Test service: curl http://${EXTERNAL_IP}:3002/health"
echo "   View logs: gcloud compute ssh ${INSTANCE_NAME} --zone=${ZONE} --project=${PROJECT_ID} --command='docker logs rtmp-service'"
echo "   Restart service: gcloud compute ssh ${INSTANCE_NAME} --zone=${ZONE} --project=${PROJECT_ID} --command='docker restart rtmp-service'"
