#!/usr/bin/env node

const { spawn } = require('child_process');

console.log('🔧 Testing RTMP Fixes for FFmpeg 7.x Compatibility...\n');

const STREAM_KEY = 'live_1048713352_TbNczEfLtlt6wRdvxXLBrfqBBQ72Ax';

// Specific fixes for FFmpeg 7.x + Twitch compatibility
const fixes = [
  {
    name: 'Fix 1: Explicit RTMP app parameter',
    url: 'rtmp://live.twitch.tv/app',
    extraArgs: ['-rtmp_app', 'live', '-rtmp_playpath', STREAM_KEY]
  },
  {
    name: 'Fix 2: Force RTMP version 3',
    url: `rtmp://live.twitch.tv/app/${STREAM_KEY}`,
    extraArgs: ['-rtmp_ver', '3']
  },
  {
    name: 'Fix 3: Disable RTMP enhanced features',
    url: `rtmp://live.twitch.tv/app/${STREAM_KEY}`,
    extraArgs: ['-rtmp_enhanced_codecs', '0']
  },
  {
    name: 'Fix 4: Legacy RTMP mode',
    url: `rtmp://live.twitch.tv/app/${STREAM_KEY}`,
    extraArgs: ['-f', 'flv', '-flvflags', 'no_duration_filesize']
  },
  {
    name: 'Fix 5: Simplified parameters',
    url: `rtmp://live.twitch.tv/app/${STREAM_KEY}`,
    extraArgs: []
  }
];

let currentFix = 0;

function testNextFix() {
  if (currentFix >= fixes.length) {
    console.log('\n❌ All fixes failed!');
    console.log('');
    console.log('🔍 Root cause analysis:');
    console.log('   • FFmpeg 7.1.1 appears incompatible with Twitch RTMP');
    console.log('   • RTMP handshake completes but publish command is rejected');
    console.log('   • This is a known issue with newer FFmpeg versions');
    console.log('');
    console.log('💡 Recommended solutions:');
    console.log('   1. Install FFmpeg 4.4.x or 5.x (known to work with Twitch)');
    console.log('   2. Use OBS Studio for testing (uses older RTMP implementation)');
    console.log('   3. Consider using SRS (Simple Realtime Server) as RTMP proxy');
    console.log('   4. Switch to WebRTC-based streaming (no RTMP needed)');
    console.log('');
    console.log('🛠️  Quick FFmpeg downgrade (if needed):');
    console.log('   brew uninstall ffmpeg');
    console.log('   brew install ffmpeg@4');
    return;
  }

  const fix = fixes[currentFix];
  console.log(`🧪 ${fix.name}...`);
  
  const baseArgs = [
    '-v', 'error',  // Minimal logging for cleaner output
    '-f', 'lavfi',
    '-i', 'testsrc=duration=5:size=1280x720:rate=30',
    '-f', 'lavfi',
    '-i', 'sine=frequency=1000:duration=5',
    '-c:v', 'libx264',
    '-preset', 'ultrafast',
    '-tune', 'zerolatency',
    '-profile:v', 'baseline',
    '-level', '3.1',
    '-pix_fmt', 'yuv420p',
    '-g', '60',
    '-keyint_min', '60',
    '-b:v', '800k',
    '-maxrate', '1000k',
    '-bufsize', '2000k',
    '-c:a', 'aac',
    '-b:a', '128k',
    '-ar', '44100',
    '-ac', '2'
  ];
  
  const args = [...baseArgs, ...fix.extraArgs, fix.url];
  
  console.log(`   Command: ffmpeg ${args.join(' ')}`);
  
  const ffmpeg = spawn('ffmpeg', args);
  let success = false;
  
  ffmpeg.stderr.on('data', (data) => {
    const output = data.toString();
    
    if (output.includes('fps=') && !success) {
      success = true;
      console.log(`   ✅ SUCCESS! ${fix.name} works!`);
      console.log('');
      console.log('🎉 Working configuration found!');
      console.log('📋 Use these parameters in your application:');
      console.log(`   URL: ${fix.url}`);
      console.log(`   Extra args: ${fix.extraArgs.join(' ')}`);
      
      ffmpeg.kill('SIGTERM');
      process.exit(0);
    }
    
    if (output.includes('Error opening output')) {
      console.log(`   ❌ Failed: RTMP publish rejected`);
    } else if (output.includes('Connection refused')) {
      console.log(`   ❌ Failed: Connection refused`);
    } else if (output.includes('timeout')) {
      console.log(`   ❌ Failed: Timeout`);
    }
  });
  
  ffmpeg.on('close', (code) => {
    if (!success) {
      console.log(`   ❌ Failed (exit code: ${code})`);
      console.log('');
      
      // Try next fix
      currentFix++;
      setTimeout(testNextFix, 1000);
    }
  });
  
  // Timeout after 8 seconds
  setTimeout(() => {
    if (!ffmpeg.killed && !success) {
      console.log(`   ⏱️ Timeout`);
      ffmpeg.kill('SIGTERM');
      
      currentFix++;
      setTimeout(testNextFix, 1000);
    }
  }, 8000);
}

// Also test if we can find an older FFmpeg version
function checkForOlderFFmpeg() {
  console.log('🔍 Checking for alternative FFmpeg versions...\n');
  
  const possiblePaths = [
    '/usr/local/bin/ffmpeg@4',
    '/usr/local/bin/ffmpeg4',
    '/opt/homebrew/bin/ffmpeg@4',
    '/usr/bin/ffmpeg'
  ];
  
  let foundAlternative = false;
  
  possiblePaths.forEach(path => {
    try {
      const { execSync } = require('child_process');
      const version = execSync(`${path} -version 2>/dev/null | head -1`, { encoding: 'utf8' });
      if (version.includes('ffmpeg version')) {
        console.log(`📍 Found: ${path} - ${version.trim()}`);
        foundAlternative = true;
      }
    } catch (e) {
      // Path doesn't exist, ignore
    }
  });
  
  if (!foundAlternative) {
    console.log('ℹ️  No alternative FFmpeg versions found');
  }
  
  console.log('');
  testNextFix();
}

checkForOlderFFmpeg();
