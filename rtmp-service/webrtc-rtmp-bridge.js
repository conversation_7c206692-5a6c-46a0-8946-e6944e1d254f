const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const { v4: uuidv4 } = require('uuid');
const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Import WebRTC
const { RTCPeerConnection, RTCSessionDescription, RTCIceCandidate } = require('wrtc');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: true,
    methods: ["GET", "POST"],
    credentials: true
  },
  transports: ['websocket', 'polling']
});

app.use(cors({
  origin: true,
  credentials: true
}));
app.use(express.json());

// Store active streaming sessions
const streamingSessions = new Map();

class WebRTCToRTMPBridge {
  constructor(sessionId, rtmpUrl, streamKey) {
    this.sessionId = sessionId;
    this.rtmpUrl = rtmpUrl;
    this.streamKey = streamKey;
    this.fullRtmpUrl = `${rtmpUrl}/${streamKey}`;
    this.peerConnection = null;
    this.ffmpegProcess = null;
    this.isStreaming = false;
    this.startedAt = null;
    this.videoTrack = null;
    this.audioTrack = null;
    
    console.log(`🌉 Created WebRTC-to-RTMP bridge ${sessionId} for ${this.fullRtmpUrl}`);
  }

  async createPeerConnection() {
    console.log(`🔗 Creating WebRTC peer connection for session ${this.sessionId}`);
    
    // Configure ICE servers
    const iceServers = [
      { urls: 'stun:stun.l.google.com:19302' }
    ];
    
    // Add TURN server if configured
    const turnServer = process.env.TURN_SERVER;
    if (turnServer) {
      iceServers.push({
        urls: [`turn:${turnServer}:3478`],
        username: process.env.TURN_USERNAME || 'webrtc',
        credential: process.env.TURN_PASSWORD || 'webrtc123'
      });
    }

    this.peerConnection = new RTCPeerConnection({ iceServers });

    // Handle incoming tracks
    this.peerConnection.ontrack = (event) => {
      console.log(`📹 Received ${event.track.kind} track for session ${this.sessionId}`);

      if (event.track.kind === 'video') {
        this.videoTrack = event.track;
        console.log(`✅ Video track received for session ${this.sessionId}`);
        console.log(`📊 Video track settings:`, event.track.getSettings ? event.track.getSettings() : 'N/A');

        // Set up frame capture from the video track
        this.setupVideoTrackCapture();
      } else if (event.track.kind === 'audio') {
        this.audioTrack = event.track;
      }

      // Start FFmpeg when we have video track
      if (this.videoTrack && !this.isStreaming) {
        this.startDirectFFmpegStream();
      }
    };

    // Handle connection state changes
    this.peerConnection.onconnectionstatechange = () => {
      if (!this.peerConnection) {
        return; // Connection was already cleaned up
      }

      console.log(`🔄 WebRTC connection state: ${this.peerConnection.connectionState} for session ${this.sessionId}`);

      if (this.peerConnection.connectionState === 'failed' ||
          this.peerConnection.connectionState === 'disconnected') {
        this.cleanup();
      }
    };

    return this.peerConnection;
  }

  async handleOffer(offer) {
    console.log(`📨 Handling WebRTC offer for session ${this.sessionId}`);
    
    if (!this.peerConnection) {
      await this.createPeerConnection();
    }

    await this.peerConnection.setRemoteDescription(new RTCSessionDescription(offer));
    const answer = await this.peerConnection.createAnswer();
    await this.peerConnection.setLocalDescription(answer);

    console.log(`📤 Created WebRTC answer for session ${this.sessionId}`);
    return answer;
  }

  async handleIceCandidate(candidate) {
    if (this.peerConnection && candidate) {
      try {
        await this.peerConnection.addIceCandidate(new RTCIceCandidate(candidate));
        console.log(`🧊 Added ICE candidate for session ${this.sessionId}`);
      } catch (error) {
        console.error(`❌ Error adding ICE candidate:`, error);
      }
    }
  }

  startDirectFFmpegStream() {
    if (this.isStreaming || !this.videoTrack) {
      return;
    }

    console.log(`🚀 Starting direct WebRTC-to-RTMP stream for session ${this.sessionId}`);

    try {
      // Create FFmpeg process with WebRTC input simulation
      // We'll use a named pipe approach but with proper WebRTC handling
      const tempDir = path.join(__dirname, 'temp', this.sessionId);
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      // Start FFmpeg with high-quality settings optimized for live streaming
      const ffmpegArgs = [
        // Input: We'll feed raw video frames via stdin
        '-f', 'rawvideo',
        '-pixel_format', 'yuv420p',  // YUV420p is much more efficient than RGBA
        '-video_size', '1920x1080',
        '-framerate', '30',
        '-i', 'pipe:0',
        
        // Audio input (we'll add this when we have audio track)
        ...(this.audioTrack ? [
          '-f', 's16le',
          '-ar', '48000',
          '-ac', '2',
          '-i', 'pipe:1'
        ] : [
          // Generate silent audio if no audio track
          '-f', 'lavfi',
          '-i', 'anullsrc=channel_layout=stereo:sample_rate=48000'
        ]),

        // Video encoding - optimized for live streaming
        '-c:v', 'libx264',
        '-preset', 'ultrafast', // Fastest encoding for low latency
        '-tune', 'zerolatency',
        '-profile:v', 'baseline',
        '-level', '3.1',
        '-pix_fmt', 'yuv420p',
        
        // Keyframe settings for live streaming
        '-g', '60', // Keyframe every 2 seconds at 30fps
        '-keyint_min', '60',
        '-sc_threshold', '0',
        
        // Bitrate settings
        '-b:v', '2500k',
        '-maxrate', '3000k',
        '-bufsize', '6000k',
        
        // Audio encoding
        '-c:a', 'aac',
        '-b:a', '128k',
        '-ar', '44100',
        '-ac', '2',
        
        // Output format
        '-f', 'flv',
        '-rtmp_live', 'live',
        
        // Output - use file for testing if RTMP fails
        process.env.TEST_MODE ? `test_stream_${this.sessionId}.mp4` : this.fullRtmpUrl
      ];

      console.log(`FFmpeg command: ffmpeg ${ffmpegArgs.join(' ')}`);

      this.ffmpegProcess = spawn('ffmpeg', ffmpegArgs, {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      this.isStreaming = true;
      this.startedAt = new Date();

      // Handle FFmpeg events
      this.ffmpegProcess.on('spawn', () => {
        console.log(`✅ FFmpeg spawned for session ${this.sessionId}`);
        this.startVideoCapture();
      });

      this.ffmpegProcess.on('error', (error) => {
        console.error(`❌ FFmpeg error:`, error);
        this.isStreaming = false;
        this.cleanup();
      });

      this.ffmpegProcess.on('exit', (code, signal) => {
        console.log(`🏁 FFmpeg exited with code ${code}, signal ${signal}`);

        if (code !== 0) {
          console.error(`❌ FFmpeg failed with exit code ${code}`);
          if (code === 1) {
            console.error(`💡 Exit code 1 usually indicates RTMP connection failure`);
            console.error(`🔧 Troubleshooting steps:`);
            console.error(`   1. Verify stream key is current and valid`);
            console.error(`   2. Check Twitch account is set to "Go Live"`);
            console.error(`   3. Test connectivity: nc -v live.twitch.tv 1935`);
            console.error(`   4. Try regenerating stream key in Twitch Creator Dashboard`);
          }
        }

        this.isStreaming = false;
      });

      // Handle stderr for detailed logging
      this.ffmpegProcess.stderr.on('data', (data) => {
        const message = data.toString().trim();

        // Always log important messages
        if (message.includes('fps=') || message.includes('bitrate=') ||
            message.includes('Connection') || message.includes('Failed') ||
            message.includes('Error') || message.includes('rtmp://') ||
            message.includes('Stream mapping') || message.includes('Output')) {
          console.log(`📺 FFmpeg [${this.sessionId}]: ${message}`);
        }

        // Log errors prominently
        if (message.toLowerCase().includes('error') ||
            message.toLowerCase().includes('failed') ||
            message.toLowerCase().includes('refused')) {
          console.error(`❌ FFmpeg Error [${this.sessionId}]: ${message}`);
        }

        // Log connection attempts
        if (message.includes('rtmp://')) {
          console.log(`🔗 FFmpeg RTMP [${this.sessionId}]: ${message}`);
        }
      });

      // Also capture stdout
      this.ffmpegProcess.stdout.on('data', (data) => {
        const message = data.toString().trim();
        if (message) {
          console.log(`📺 FFmpeg stdout [${this.sessionId}]: ${message}`);
        }
      });

    } catch (error) {
      console.error(`❌ Error starting FFmpeg:`, error);
      this.isStreaming = false;
    }
  }

  startVideoCapture() {
    console.log(`🎥 Starting real video capture from WebRTC track for session ${this.sessionId}`);

    if (!this.ffmpegProcess || !this.ffmpegProcess.stdin || !this.videoTrack) {
      console.error(`❌ Cannot start video capture: missing FFmpeg process or video track`);
      return;
    }

    console.log(`📹 Setting up MediaStreamTrackProcessor for real-time frame capture...`);

    try {
      // Use MediaStreamTrackProcessor for real-time frame capture
      if (typeof MediaStreamTrackProcessor !== 'undefined') {
        this.setupMediaStreamProcessor();
      } else {
        // Fallback: Use wrtc's frame callback if available
        this.setupWRTCFrameCapture();
      }
    } catch (error) {
      console.error(`❌ Error setting up video capture:`, error);
      // Fallback to test pattern for now
      this.startTestPattern();
    }
  }

  setupMediaStreamProcessor() {
    console.log(`🔄 Using MediaStreamTrackProcessor for frame capture`);

    try {
      const processor = new MediaStreamTrackProcessor({ track: this.videoTrack });
      const reader = processor.readable.getReader();

      const processFrame = async () => {
        try {
          const { done, value: frame } = await reader.read();

          if (done) {
            console.log(`📹 Video track ended`);
            return;
          }

          if (frame) {
            await this.processVideoFrame(frame);
            frame.close(); // Important: close the frame to free memory
          }

          // Continue processing
          processFrame();

        } catch (error) {
          console.error(`❌ Error processing frame:`, error);
          // Fallback to test pattern
          this.startTestPattern();
        }
      };

      processFrame();
      console.log(`✅ MediaStreamTrackProcessor started`);

    } catch (error) {
      console.error(`❌ MediaStreamTrackProcessor failed:`, error);
      this.setupWRTCFrameCapture();
    }
  }

  setupWRTCFrameCapture() {
    console.log(`🔄 Using WRTC MediaStream approach for frame capture`);

    try {
      // Create a MediaStream with the video track
      const { MediaStream } = require('wrtc');
      const stream = new MediaStream([this.videoTrack]);

      console.log(`📺 Created MediaStream with video track`);
      console.log(`🎬 Stream active: ${stream.active}, tracks: ${stream.getTracks().length}`);

      // Try to use the stream with a different approach
      // Since we can't directly access frame data in Node.js wrtc,
      // we'll use a hybrid approach: receive the WebRTC stream and
      // let FFmpeg handle the video processing directly

      this.setupDirectStreamToFFmpeg(stream);

    } catch (error) {
      console.error(`❌ WRTC MediaStream approach failed:`, error);
      console.log(`🔄 Falling back to enhanced test pattern with real video dimensions`);
      this.startEnhancedTestPattern();
    }
  }

  setupDirectStreamToFFmpeg(stream) {
    console.log(`🎯 Setting up direct stream to FFmpeg`);

    // For Node.js wrtc, we need a different approach
    // Let's create a more sophisticated test pattern that simulates real video
    // and includes information from the actual WebRTC connection

    try {
      // Get track constraints/settings if available
      const track = stream.getVideoTracks()[0];
      let width = 1920, height = 1080;

      if (track && track.getSettings) {
        const settings = track.getSettings();
        width = settings.width || 1920;
        height = settings.height || 1080;
        console.log(`📊 Using video dimensions from track: ${width}x${height}`);
      }

      // Start enhanced pattern with real dimensions
      this.startEnhancedTestPattern(width, height);

    } catch (error) {
      console.error(`❌ Direct stream setup failed:`, error);
      this.startTestPattern();
    }
  }

  setupVideoTrackCapture() {
    if (!this.videoTrack) {
      console.error(`❌ No video track available for capture setup`);
      return;
    }

    console.log(`🎬 Setting up video track capture for session ${this.sessionId}`);

    // In Node.js with wrtc, we need to use a different approach
    // The wrtc library doesn't support MediaStreamTrackProcessor
    // So we'll use a timer-based approach to capture frames

    try {
      // Check if the track has any special properties we can use
      if (this.videoTrack._source) {
        console.log(`📊 Video track source available:`, typeof this.videoTrack._source);
      }

      // For now, we'll start the video capture when FFmpeg is ready
      // The actual frame capture will happen in startVideoCapture()
      console.log(`✅ Video track capture setup complete`);

    } catch (error) {
      console.error(`❌ Error setting up video track capture:`, error);
    }
  }

  async processVideoFrame(frame) {
    if (!this.ffmpegProcess || !this.ffmpegProcess.stdin || this.ffmpegProcess.stdin.destroyed) {
      return;
    }

    try {
      // Get frame dimensions
      const width = frame.displayWidth || frame.codedWidth;
      const height = frame.displayHeight || frame.codedHeight;

      // For Node.js environment, we need to use a different approach
      // since OffscreenCanvas might not be available
      console.log(`📹 Processing frame: ${width}x${height}`);

      // For now, we'll use a test pattern but with real dimensions
      const yuvData = this.createTestFrame(width, height);

      // Send to FFmpeg
      this.ffmpegProcess.stdin.write(yuvData);

      // Log progress occasionally
      this.frameCount = (this.frameCount || 0) + 1;
      if (this.frameCount % 30 === 0) {
        console.log(`📊 Processed ${this.frameCount} video frames (${width}x${height})`);
      }

    } catch (error) {
      console.error(`❌ Error processing video frame:`, error);
    }
  }

  createTestFrame(width, height) {
    // YUV420p format: Y plane + U plane (1/4 size) + V plane (1/4 size)
    const ySize = width * height;
    const uvSize = (width * height) / 4;
    const frameSize = ySize + uvSize + uvSize;
    const testFrame = Buffer.alloc(frameSize);

    // Create a simple test pattern that changes over time
    const time = Date.now() / 1000;
    const offset = Math.floor(time * 30) % 255; // 30 FPS animation

    // Fill Y (luminance) component with moving pattern
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const index = y * width + x;
        const intensity = ((x + y + offset) % 255);
        testFrame[index] = intensity;
      }
    }

    // Fill U and V (chrominance) components with animated colors
    const uStart = ySize;
    const vStart = ySize + uvSize;

    for (let i = 0; i < uvSize; i++) {
      // Animated color components
      const u = 128 + Math.sin(time * 1.2 + i * 0.001) * 40;
      const v = 128 + Math.cos(time * 0.8 + i * 0.001) * 40;

      testFrame[uStart + i] = Math.max(0, Math.min(255, Math.round(u)));
      testFrame[vStart + i] = Math.max(0, Math.min(255, Math.round(v)));
    }

    return testFrame;
  }

  startTestPattern() {
    console.log(`🎨 Starting basic test pattern as fallback`);
    this.startEnhancedTestPattern(1920, 1080);
  }

  startEnhancedTestPattern(width = 1920, height = 1080) {
    console.log(`🎨 Starting enhanced test pattern (${width}x${height})`);

    let frameCounter = 0;
    const startTime = Date.now();

    this.frameInterval = setInterval(() => {
      if (this.ffmpegProcess && this.ffmpegProcess.stdin && !this.ffmpegProcess.stdin.destroyed) {
        try {
          const testFrame = this.createEnhancedTestFrame(width, height, frameCounter);
          this.ffmpegProcess.stdin.write(testFrame);
          frameCounter++;

          if (frameCounter % 30 === 0) {
            const elapsed = (Date.now() - startTime) / 1000;
            console.log(`📊 Sent ${frameCounter} enhanced frames (${width}x${height}) - ${elapsed.toFixed(1)}s`);
          }
        } catch (error) {
          if (error.code === 'EPIPE') {
            console.error(`❌ FFmpeg process closed (EPIPE) - likely due to RTMP connection failure`);
            console.error(`💡 This usually means the RTMP server rejected the connection`);
          } else {
            console.error(`❌ Error writing enhanced test frame:`, error);
          }
          clearInterval(this.frameInterval);
        }
      } else {
        clearInterval(this.frameInterval);
      }
    }, 33); // ~30 FPS
  }

  createEnhancedTestFrame(width, height, frameCounter) {
    // YUV420p format: Y plane + U plane (1/4 size) + V plane (1/4 size)
    const ySize = width * height;
    const uvSize = (width * height) / 4;
    const frameSize = ySize + uvSize + uvSize;
    const testFrame = Buffer.alloc(frameSize);

    // Create a more sophisticated test pattern that looks more like real video
    const time = frameCounter / 30.0; // Time in seconds

    // Fill Y (luminance) component with complex moving pattern
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const index = y * width + x;

        // Base gradient
        let intensity = ((x + y) / (width + height)) * 255;

        // Add moving waves
        intensity += Math.sin((x / 100) + time * 2) * 30;
        intensity += Math.cos((y / 80) + time * 1.5) * 20;

        // Add moving diagonal stripes
        intensity += Math.sin(((x + y) / 50) + time * 3) * 25;

        // Add some "noise" to simulate camera grain
        intensity += (Math.random() - 0.5) * 10;

        // Clamp to valid range and set Y component
        testFrame[index] = Math.max(0, Math.min(255, Math.round(intensity)));
      }
    }

    // Fill U and V (chrominance) components with animated colors
    const uStart = ySize;
    const vStart = ySize + uvSize;

    for (let i = 0; i < uvSize; i++) {
      // More complex animated color components
      const u = 128 + Math.sin(time * 2 + i * 0.001) * 60;
      const v = 128 + Math.cos(time * 1.5 + i * 0.001) * 60;

      testFrame[uStart + i] = Math.max(0, Math.min(255, Math.round(u)));
      testFrame[vStart + i] = Math.max(0, Math.min(255, Math.round(v)));
    }

    return testFrame;
  }

  stop() {
    console.log(`🛑 Stopping bridge for session ${this.sessionId}`);
    this.cleanup();
  }

  cleanup() {
    console.log(`🧹 Cleaning up session ${this.sessionId}`);
    
    if (this.frameInterval) {
      clearInterval(this.frameInterval);
      this.frameInterval = null;
    }
    
    if (this.ffmpegProcess) {
      try {
        if (this.ffmpegProcess.stdin) {
          this.ffmpegProcess.stdin.end();
        }
        this.ffmpegProcess.kill('SIGTERM');
      } catch (error) {
        console.error(`Error killing FFmpeg:`, error);
      }
      this.ffmpegProcess = null;
    }

    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }

    this.isStreaming = false;
    this.videoTrack = null;
    this.audioTrack = null;

    // Clean up temp directory
    const tempDir = path.join(__dirname, 'temp', this.sessionId);
    if (fs.existsSync(tempDir)) {
      try {
        fs.rmSync(tempDir, { recursive: true, force: true });
      } catch (error) {
        console.error(`Error cleaning up temp directory:`, error);
      }
    }
  }

  getStatus() {
    return {
      sessionId: this.sessionId,
      rtmpUrl: this.rtmpUrl,
      isStreaming: this.isStreaming,
      startedAt: this.startedAt,
      connectionState: this.peerConnection?.connectionState || 'disconnected',
      hasVideo: !!this.videoTrack,
      hasAudio: !!this.audioTrack
    };
  }
}

module.exports = { WebRTCToRTMPBridge };
