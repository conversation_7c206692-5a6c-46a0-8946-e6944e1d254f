#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔑 Twitch Stream Key Updater\n');

// Get new stream key from command line argument
const newStreamKey = process.argv[2];

if (!newStreamKey) {
  console.log('❌ Please provide a new stream key as an argument');
  console.log('');
  console.log('Usage: node update-stream-key.js YOUR_NEW_STREAM_KEY');
  console.log('');
  console.log('📝 To get your stream key:');
  console.log('   1. Go to https://dashboard.twitch.tv/');
  console.log('   2. Navigate to Settings > Stream');
  console.log('   3. Copy the Primary Stream Key');
  console.log('   4. Make sure "Go Live" is enabled');
  console.log('');
  console.log('Example:');
  console.log('   node update-stream-key.js live_123456789_AbCdEfGhIjKlMnOpQrStUvWxYz');
  process.exit(1);
}

// Validate stream key format (basic check)
if (!newStreamKey.startsWith('live_') || newStreamKey.length < 20) {
  console.log('⚠️  Warning: Stream key format looks unusual');
  console.log('   Expected format: live_XXXXXXXXX_XXXXXXXXXXXXXXXXXXXXXXXX');
  console.log('   Your key: ' + newStreamKey);
  console.log('');
  
  const readline = require('readline').createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  readline.question('Continue anyway? (y/N): ', (answer) => {
    readline.close();
    if (answer.toLowerCase() !== 'y' && answer.toLowerCase() !== 'yes') {
      console.log('❌ Cancelled');
      process.exit(1);
    }
    updateStreamKeys();
  });
} else {
  updateStreamKeys();
}

function updateStreamKeys() {
  const oldStreamKey = 'live_1048713352_TbNczEfLtlt6wRdvxXLBrfqBBQ72Ax';
  
  console.log('🔄 Updating stream keys in codebase...');
  console.log(`   Old key: ${oldStreamKey.substring(0, 8)}...${oldStreamKey.substring(oldStreamKey.length - 4)}`);
  console.log(`   New key: ${newStreamKey.substring(0, 8)}...${newStreamKey.substring(newStreamKey.length - 4)}`);
  console.log('');

  // Files to update
  const filesToUpdate = [
    'test-twitch-connection.js',
    'test-twitch-fixed.js',
    'test-streaming.js',
    '../src/components/HostInterface.tsx'
  ];

  let updatedFiles = 0;
  let errors = 0;

  filesToUpdate.forEach(filePath => {
    try {
      const fullPath = path.resolve(__dirname, filePath);
      
      if (!fs.existsSync(fullPath)) {
        console.log(`⚠️  File not found: ${filePath}`);
        return;
      }

      const content = fs.readFileSync(fullPath, 'utf8');
      
      if (!content.includes(oldStreamKey)) {
        console.log(`ℹ️  No stream key found in: ${filePath}`);
        return;
      }

      const updatedContent = content.replace(new RegExp(oldStreamKey, 'g'), newStreamKey);
      fs.writeFileSync(fullPath, updatedContent, 'utf8');
      
      console.log(`✅ Updated: ${filePath}`);
      updatedFiles++;
      
    } catch (error) {
      console.error(`❌ Error updating ${filePath}:`, error.message);
      errors++;
    }
  });

  console.log('');
  console.log(`📊 Summary:`);
  console.log(`   Files updated: ${updatedFiles}`);
  console.log(`   Errors: ${errors}`);
  
  if (updatedFiles > 0) {
    console.log('');
    console.log('🧪 Next steps:');
    console.log('   1. Test the new stream key:');
    console.log(`      node test-with-new-key.js ${newStreamKey}`);
    console.log('   2. If successful, restart your application');
    console.log('   3. Try streaming from the web interface');
  }
}
