#!/bin/bash

# Deploy WebRTC to RTMP Streaming Service to Google Compute Engine
# This script builds the Docker image locally, pushes it to GCR, and deploys to GCE

set -e

# Configuration
PROJECT_ID=${GOOGLE_CLOUD_PROJECT:-"switcher-studio-233517"}
REGION=${REGION:-"us-central1"}
ZONE=${ZONE:-"us-central1-a"}
INSTANCE_NAME="rtmp-streaming-service"
MACHINE_TYPE="e2-standard-2"  # More CPU for FFmpeg processing
IMAGE_FAMILY="cos-stable"
IMAGE_PROJECT="cos-cloud"
NETWORK_TAG="rtmp-service"
REPOSITORY_NAME="rtmp-repo"
DOCKER_IMAGE="${REGION}-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY_NAME}/rtmp-streaming-service"

echo "🚀 Deploying WebRTC to RTMP Streaming Service to Google Compute Engine..."
echo "Project: ${PROJECT_ID}"
echo "Region: ${REGION}"
echo "Zone: ${ZONE}"
echo "Instance: ${INSTANCE_NAME}"

# Create Artifact Registry repository if it doesn't exist
echo "📦 Setting up Artifact Registry repository..."
if ! gcloud artifacts repositories describe ${REPOSITORY_NAME} --location=${REGION} --project=${PROJECT_ID} &>/dev/null; then
    echo "Creating Artifact Registry repository: ${REPOSITORY_NAME}"
    gcloud artifacts repositories create ${REPOSITORY_NAME} \
        --repository-format=docker \
        --location=${REGION} \
        --description="Docker repository for RTMP streaming service" \
        --project=${PROJECT_ID}
else
    echo "Artifact Registry repository ${REPOSITORY_NAME} already exists"
fi

# Configure Docker authentication for Artifact Registry
echo "🔐 Configuring Docker authentication..."
gcloud auth configure-docker ${REGION}-docker.pkg.dev --quiet

# Build and push Docker image
echo "📦 Building Docker image..."
docker build -t ${DOCKER_IMAGE} .

echo "📤 Pushing image to Artifact Registry..."
docker push ${DOCKER_IMAGE}

# Check if instance already exists
if gcloud compute instances describe ${INSTANCE_NAME} --zone=${ZONE} --project=${PROJECT_ID} &>/dev/null; then
    echo "⚠️  Instance ${INSTANCE_NAME} already exists. Stopping and deleting..."
    gcloud compute instances delete ${INSTANCE_NAME} \
        --zone=${ZONE} \
        --project=${PROJECT_ID} \
        --quiet
    echo "✅ Existing instance deleted"
fi

# Create firewall rules for RTMP service
echo "🔥 Creating firewall rules..."
gcloud compute firewall-rules delete ${NETWORK_TAG}-allow-rtmp --project=${PROJECT_ID} --quiet 2>/dev/null || true
gcloud compute firewall-rules create ${NETWORK_TAG}-allow-rtmp \
    --allow tcp:3002,tcp:80,tcp:443 \
    --source-ranges 0.0.0.0/0 \
    --target-tags ${NETWORK_TAG} \
    --description "Allow RTMP service traffic" \
    --project=${PROJECT_ID}

# Create startup script for the instance
cat > startup-script.sh << EOF
#!/bin/bash

# Configure Docker authentication for Artifact Registry
gcloud auth configure-docker ${REGION}-docker.pkg.dev --quiet

# Pull the RTMP service image
docker pull ${DOCKER_IMAGE}

# Stop any existing RTMP service container
docker stop rtmp-service 2>/dev/null || true
docker rm rtmp-service 2>/dev/null || true

# Create directories for logs and temp files
mkdir -p /opt/rtmp-service/logs
mkdir -p /opt/rtmp-service/temp

# Run RTMP service container
docker run -d \\
    --name rtmp-service \\
    --restart unless-stopped \\
    -p 3002:3002 \\
    -v /opt/rtmp-service/logs:/usr/src/app/logs \\
    -v /opt/rtmp-service/temp:/usr/src/app/temp \\
    -e NODE_ENV=production \\
    -e PORT=3002 \\
    ${DOCKER_IMAGE}

echo "✅ RTMP Streaming Service started successfully"

# Set up log rotation for Docker containers
cat > /etc/logrotate.d/rtmp-service << 'LOGROTATE_EOF'
/var/lib/docker/containers/*/*-json.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 0644 root root
    postrotate
        docker kill --signal="USR1" rtmp-service 2>/dev/null || true
    endscript
}
/opt/rtmp-service/logs/*.log {
    daily
    rotate 14
    compress
    delaycompress
    missingok
    notifempty
    create 0644 root root
}
LOGROTATE_EOF

echo "✅ Log rotation configured"

# Install monitoring tools
apt-get update
apt-get install -y htop iotop nethogs

echo "✅ Monitoring tools installed"
EOF

# Create the GCE instance
echo "🖥️  Creating GCE instance..."
gcloud compute instances create ${INSTANCE_NAME} \
    --zone=${ZONE} \
    --machine-type=${MACHINE_TYPE} \
    --network-interface=network-tier=PREMIUM,subnet=default \
    --maintenance-policy=MIGRATE \
    --provisioning-model=STANDARD \
    --service-account=$(gcloud iam service-accounts list --filter="displayName:Compute Engine default service account" --format="value(email)" --project=${PROJECT_ID}) \
    --scopes=https://www.googleapis.com/auth/devstorage.read_only,https://www.googleapis.com/auth/logging.write,https://www.googleapis.com/auth/monitoring.write,https://www.googleapis.com/auth/servicecontrol,https://www.googleapis.com/auth/service.management.readonly,https://www.googleapis.com/auth/trace.append \
    --tags=${NETWORK_TAG} \
    --image-family=${IMAGE_FAMILY} \
    --image-project=${IMAGE_PROJECT} \
    --boot-disk-size=20GB \
    --boot-disk-type=pd-balanced \
    --boot-disk-device-name=${INSTANCE_NAME} \
    --metadata-from-file startup-script=startup-script.sh \
    --project=${PROJECT_ID}

# Wait for instance to be ready
echo "⏳ Waiting for instance to start..."
sleep 60

# Get the external IP
EXTERNAL_IP=$(gcloud compute instances describe ${INSTANCE_NAME} \
    --zone=${ZONE} \
    --format='get(networkInterfaces[0].accessConfigs[0].natIP)' \
    --project=${PROJECT_ID})

# Clean up startup script
rm -f startup-script.sh

echo "✅ Deployment complete!"
echo "🔗 Instance: ${INSTANCE_NAME}"
echo "🌐 External IP: ${EXTERNAL_IP}"
echo "🎯 RTMP Service: http://${EXTERNAL_IP}:3002"
echo ""
echo "📝 Update your signaling server configuration to use:"
echo "   RTMP_SERVICE_URL=http://${EXTERNAL_IP}:3002"
echo ""
echo "🔧 Management commands:"
echo "   SSH to instance: gcloud compute ssh ${INSTANCE_NAME} --zone=${ZONE} --project=${PROJECT_ID}"
echo "   View logs: gcloud compute ssh ${INSTANCE_NAME} --zone=${ZONE} --project=${PROJECT_ID} --command='docker logs rtmp-service'"
echo "   View service status: curl http://${EXTERNAL_IP}:3002/health"
echo "   View active sessions: curl http://${EXTERNAL_IP}:3002/sessions"
echo "   Restart service: gcloud compute ssh ${INSTANCE_NAME} --zone=${ZONE} --project=${PROJECT_ID} --command='docker restart rtmp-service'"
echo "   Stop instance: gcloud compute instances stop ${INSTANCE_NAME} --zone=${ZONE} --project=${PROJECT_ID}"
echo "   Delete instance: gcloud compute instances delete ${INSTANCE_NAME} --zone=${ZONE} --project=${PROJECT_ID}"
echo ""
echo "🔥 Firewall rules created for ports:"
echo "   - TCP 3002 (RTMP Service API)"
echo "   - TCP 80/443 (HTTP/HTTPS)"
echo ""
echo "⚠️  Note: It may take a few minutes for the service to be fully ready."
echo "   You can check the status with: curl http://${EXTERNAL_IP}:3002/health"
