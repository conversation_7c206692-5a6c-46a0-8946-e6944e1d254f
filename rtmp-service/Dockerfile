# Dockerfile for WebRTC to RTMP Streaming Service
FROM node:18-bullseye

# Install FFmpeg and dependencies
RUN apt-get update && apt-get install -y \
    ffmpeg \
    build-essential \
    python3 \
    python3-pip \
    pkg-config \
    libavformat-dev \
    libavcodec-dev \
    libavdevice-dev \
    libavutil-dev \
    libswscale-dev \
    libswresample-dev \
    libavfilter-dev \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /usr/src/app

# Create non-root user
RUN groupadd -r rtmpuser && useradd -r -g rtmpuser rtmpuser

# Copy package files
COPY package*.json ./

# Install Node.js dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy application code
COPY . .

# Create temp directory for processing
RUN mkdir -p temp && chown -R rtmpuser:rtmpuser temp

# Create logs directory
RUN mkdir -p logs && chown -R rtmpuser:rtmpuser logs

# Change ownership of the app directory
RUN chown -R rtmpuser:rtmpuser /usr/src/app

# Expose the service port
EXPOSE 3002

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3002/health || exit 1

# Switch to non-root user
USER rtmpuser

# Set environment variables
ENV NODE_ENV=production
ENV PORT=3002

# Start the service
CMD ["node", "index.js"]
