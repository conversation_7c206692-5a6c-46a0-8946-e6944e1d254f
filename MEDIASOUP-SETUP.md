# Mediasoup Development Setup

This guide shows how to run your WebRTC-to-RTMP streaming app with the new mediasoup service.

## Quick Start

### 1. Setup Mediasoup Service

```bash
# Install mediasoup service dependencies
cd mediasoup-service
npm install
cd ..
```

### 2. Start Development Environment

**Option A: Automatic (Recommended)**
```bash
npm run dev:mediasoup
```

**Option B: Manual**
```bash
# Terminal 1: Start mediasoup service
cd mediasoup-service
npm start

# Terminal 2: Start frontend (in main directory)
npm run dev:client
```

### 3. Test the Setup

1. Open http://localhost:5173 in your browser
2. Click "Host Stream" 
3. Enter your Twitch stream key
4. Click "Start Streaming"
5. The frontend will now connect to mediasoup service at http://localhost:3000

## What Changed

### Service URL
- **Old**: `http://localhost:3002` (Node.js RTMP service)
- **New**: `http://localhost:3000` (Mediasoup service)

### Architecture
- **Old**: Node.js service with test patterns
- **New**: Mediasoup service with real WebRTC processing

### Performance
- **Old**: ~50% CPU usage, test patterns only
- **New**: ~5-10% CPU usage, real video processing

## Configuration

The frontend automatically connects to the mediasoup service via:

```javascript
// In HostInterface.tsx
const rtmpServiceUrl = import.meta.env.VITE_RTMP_SERVICE_URL || 'http://localhost:3000';
```

Environment variables (`.env`):
```bash
VITE_RTMP_SERVICE_URL=http://localhost:3000
```

## API Compatibility

The mediasoup service uses the same API as your existing RTMPService:

- ✅ `start-stream` event
- ✅ `webrtc-offer` event  
- ✅ `webrtc-answer` event
- ✅ `stop-stream` event
- ✅ Session management
- ✅ Health checks

## Troubleshooting

### Port 3000 already in use
```bash
# Find what's using port 3000
lsof -ti:3000

# Kill the process
kill -9 $(lsof -ti:3000)
```

### Mediasoup compilation issues
```bash
# Make sure you have build tools (macOS)
xcode-select --install

# Clear and reinstall
cd mediasoup-service
rm -rf node_modules package-lock.json
npm install
```

### WebRTC connection fails
- Check that UDP ports 40000-49999 are available
- Verify firewall settings
- Check browser console for WebRTC errors

### FFmpeg not found
```bash
# Install FFmpeg
brew install ffmpeg

# Or specify path in mediasoup-service/.env
FFMPEG_PATH=/usr/local/bin/ffmpeg
```

## Testing

### Health Check
```bash
curl http://localhost:3000/health
```

### Session Management
```bash
# List sessions
curl http://localhost:3000/sessions

# Get session details
curl http://localhost:3000/sessions/session-id
```

### Mock Test
```bash
cd mediasoup-service
STREAM_KEY=your-twitch-key node test-client.js
```

## Next Steps

1. **Test with real stream**: Use your actual Twitch stream key
2. **Monitor performance**: Check CPU usage vs old service
3. **Scale testing**: Try multiple concurrent streams
4. **Production deployment**: Use Docker for production

## Reverting to Old Service

If you need to revert to the old Node.js service:

```bash
# Update .env
VITE_RTMP_SERVICE_URL=http://localhost:3002

# Start old service
cd rtmp-service
npm start

# Use original dev script
npm run dev
```
