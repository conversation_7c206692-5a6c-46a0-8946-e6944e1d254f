<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebRTC Browser Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        video { width: 320px; height: 240px; border: 1px solid #ccc; margin: 10px; }
        .log { background: #f0f0f0; padding: 10px; margin: 10px 0; height: 300px; overflow-y: scroll; font-family: monospace; font-size: 12px; }
        button { padding: 10px 20px; margin: 5px; font-size: 16px; }
        .stats { background: #e8f5e8; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 WebRTC Browser Test</h1>
        
        <div>
            <button onclick="startCamera()">📹 Start Camera</button>
            <button onclick="createPeerConnection()">🔗 Create Peer Connection</button>
            <button onclick="addTracksToPC()">➕ Add Tracks to PC</button>
            <button onclick="createOffer()">📤 Create Offer</button>
            <button onclick="getStats()">📊 Get Stats</button>
            <button onclick="clearLog()">🗑️ Clear Log</button>
        </div>

        <div>
            <video id="localVideo" autoplay muted playsinline></video>
            <video id="remoteVideo" autoplay playsinline></video>
        </div>

        <div class="stats" id="stats">
            <strong>Stats:</strong> Click "Get Stats" to see WebRTC statistics
        </div>

        <div class="log" id="log"></div>
    </div>

    <script>
        let localStream = null;
        let peerConnection = null;
        let localVideo = document.getElementById('localVideo');
        let remoteVideo = document.getElementById('remoteVideo');
        let logDiv = document.getElementById('log');
        let statsDiv = document.getElementById('stats');

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            logDiv.innerHTML = '';
        }

        async function startCamera() {
            try {
                log('🎥 Requesting camera access...');
                
                localStream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 1920 },
                        height: { ideal: 1080 },
                        frameRate: { ideal: 30 }
                    },
                    audio: true
                });

                localVideo.srcObject = localStream;
                
                const videoTrack = localStream.getVideoTracks()[0];
                const audioTrack = localStream.getAudioTracks()[0];
                
                log(`✅ Camera started successfully`);
                log(`📹 Video track: ${videoTrack.id}, enabled: ${videoTrack.enabled}, readyState: ${videoTrack.readyState}`);
                log(`🎵 Audio track: ${audioTrack ? audioTrack.id : 'none'}, enabled: ${audioTrack ? audioTrack.enabled : 'N/A'}`);
                
                // Get video track settings
                const settings = videoTrack.getSettings();
                log(`📐 Video settings: ${settings.width}x${settings.height} @ ${settings.frameRate}fps`);
                
            } catch (error) {
                log(`❌ Camera error: ${error.message}`);
            }
        }

        function createPeerConnection() {
            try {
                log('🔗 Creating peer connection...');
                
                peerConnection = new RTCPeerConnection({
                    iceServers: [
                        { urls: 'stun:stun.l.google.com:19302' }
                    ]
                });

                // Log connection state changes
                peerConnection.onconnectionstatechange = () => {
                    log(`🔄 Connection state: ${peerConnection.connectionState}`);
                };

                peerConnection.oniceconnectionstatechange = () => {
                    log(`🧊 ICE connection state: ${peerConnection.iceConnectionState}`);
                };

                peerConnection.onicegatheringstatechange = () => {
                    log(`🧊 ICE gathering state: ${peerConnection.iceGatheringState}`);
                };

                peerConnection.onicecandidate = (event) => {
                    if (event.candidate) {
                        log(`🧊 ICE candidate: ${event.candidate.candidate}`);
                    } else {
                        log('🧊 ICE gathering complete');
                    }
                };

                // Track events
                peerConnection.ontrack = (event) => {
                    log(`📥 Received remote track: ${event.track.kind}`);
                    if (event.track.kind === 'video') {
                        remoteVideo.srcObject = event.streams[0];
                    }
                };

                log('✅ Peer connection created');
                
            } catch (error) {
                log(`❌ Peer connection error: ${error.message}`);
            }
        }

        function addTracksToPC() {
            if (!localStream) {
                log('❌ No local stream available. Start camera first.');
                return;
            }
            
            if (!peerConnection) {
                log('❌ No peer connection available. Create peer connection first.');
                return;
            }

            try {
                log('➕ Adding tracks to peer connection...');
                
                localStream.getTracks().forEach(track => {
                    const sender = peerConnection.addTrack(track, localStream);
                    log(`✅ Added ${track.kind} track: ${track.id}`);
                    
                    // Log track details
                    if (track.kind === 'video') {
                        const settings = track.getSettings();
                        log(`📹 Video track settings: ${JSON.stringify(settings, null, 2)}`);
                    }
                });

                // Check senders
                const senders = peerConnection.getSenders();
                log(`📤 Total senders: ${senders.length}`);
                senders.forEach((sender, index) => {
                    if (sender.track) {
                        log(`📤 Sender ${index}: ${sender.track.kind} track ${sender.track.id}`);
                    }
                });
                
            } catch (error) {
                log(`❌ Add tracks error: ${error.message}`);
            }
        }

        async function createOffer() {
            if (!peerConnection) {
                log('❌ No peer connection available.');
                return;
            }

            try {
                log('📤 Creating offer...');
                
                const offer = await peerConnection.createOffer();
                await peerConnection.setLocalDescription(offer);
                
                log('✅ Offer created and set as local description');
                log(`📋 Offer SDP:\n${offer.sdp}`);
                
                // Extract video codec info
                const videoCodec = offer.sdp.match(/a=rtpmap:(\d+)\s+(\w+)/g);
                if (videoCodec) {
                    log(`🎬 Video codecs: ${videoCodec.join(', ')}`);
                }
                
            } catch (error) {
                log(`❌ Create offer error: ${error.message}`);
            }
        }

        async function getStats() {
            if (!peerConnection) {
                log('❌ No peer connection available.');
                return;
            }

            try {
                const stats = await peerConnection.getStats();
                let statsText = '<strong>WebRTC Stats:</strong><br>';
                
                stats.forEach((report) => {
                    if (report.type === 'outbound-rtp' && report.kind === 'video') {
                        statsText += `📹 Video Outbound: ${report.bytesSent} bytes, ${report.packetsSent} packets, ${report.framesEncoded} frames<br>`;
                    } else if (report.type === 'track' && report.kind === 'video') {
                        statsText += `📹 Video Track: ${report.framesSent} frames sent, ${report.framesPerSecond} fps<br>`;
                    } else if (report.type === 'media-source' && report.kind === 'video') {
                        statsText += `📹 Video Source: ${report.width}x${report.height}, ${report.framesPerSecond} fps<br>`;
                    }
                });
                
                statsDiv.innerHTML = statsText;
                log('📊 Stats updated');
                
            } catch (error) {
                log(`❌ Get stats error: ${error.message}`);
            }
        }

        // Auto-start sequence for quick testing
        log('🚀 Browser WebRTC Test Ready');
        log('👆 Click buttons in order: Start Camera → Create Peer Connection → Add Tracks → Create Offer → Get Stats');
    </script>
</body>
</html>
