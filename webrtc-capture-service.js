const express = require('express');
const cors = require('cors');
const { spawn } = require('child_process');
const { AccessToken, RoomServiceClient } = require('livekit-server-sdk');

const app = express();
const port = 3002;

app.use(cors());
app.use(express.json());

// Cloud configuration with new IP
const LIVEKIT_URL = 'http://localhost:7880';
const LIVEKIT_WS_URL = 'ws://************:7880';
const API_KEY = 'APIUQUwG76Wo6Xd';
const API_SECRET = 'wqVmuTKidmOdyAD9bbJAoWpucQrMfDEB6MZIiydiIfq';
const TWITCH_STREAM_KEY = 'live_1048713352_TbNczEfLtlt6wRdvxXLBrfqBBQ72Ax';

// Store active FFmpeg processes
const activeStreams = new Map();

// Create room service client
const roomService = new RoomServiceClient(LIVEKIT_URL, API_KEY, API_SECRET);

app.post('/token', async (req, res) => {
  try {
    const { roomName, participantName } = req.body;
    
    if (!roomName || !participantName) {
      return res.status(400).json({ error: 'roomName and participantName are required' });
    }

    const token = new AccessToken(API_KEY, API_SECRET, {
      identity: participantName,
      ttl: '1h',
    });

    token.addGrant({
      room: roomName,
      roomJoin: true,
      canPublish: true,
      canSubscribe: true,
    });

    const jwt = await token.toJwt();
    
    console.log(`✅ Generated token for ${participantName} in room ${roomName}`);
    
    res.json({ 
      token: jwt,
      url: LIVEKIT_WS_URL,
      roomName,
      participantName
    });
  } catch (error) {
    console.error('❌ Error generating token:', error);
    res.status(500).json({ error: 'Failed to generate token' });
  }
});

app.post('/start-stream', async (req, res) => {
  try {
    const { roomName } = req.body;
    
    if (!roomName) {
      return res.status(400).json({ error: 'roomName is required' });
    }

    console.log(`🚀 Starting WebRTC capture stream for room: ${roomName}`);

    // Check if stream is already active
    if (activeStreams.has(roomName)) {
      return res.json({
        success: true,
        message: 'Stream already active',
        streamId: activeStreams.get(roomName).id
      });
    }

    const streamId = `stream-${Date.now()}`;
    
    // Create a viewer token for capturing
    const viewerToken = new AccessToken(API_KEY, API_SECRET, {
      identity: `viewer-${streamId}`,
      ttl: '2h',
    });

    viewerToken.addGrant({
      room: roomName,
      roomJoin: true,
      canPublish: false,
      canSubscribe: true,
    });

    const viewerJwt = await viewerToken.toJwt();

    // Start FFmpeg with a more sophisticated approach
    // We'll create a virtual display and use a browser to capture WebRTC
    const ffmpegArgs = [
      // Create a virtual framebuffer display
      '-f', 'x11grab',
      '-video_size', '1920x1080',
      '-framerate', '30',
      '-i', ':99',  // Virtual display
      
      // Audio input (we'll add this later)
      '-f', 'pulse',
      '-i', 'default',
      
      // Video encoding for Twitch
      '-c:v', 'libx264',
      '-preset', 'veryfast',
      '-tune', 'zerolatency',
      '-profile:v', 'main',
      '-level', '4.0',
      '-pix_fmt', 'yuv420p',
      '-g', '60',
      '-keyint_min', '60',
      '-sc_threshold', '0',
      '-b:v', '2500k',
      '-maxrate', '3000k',
      '-bufsize', '6000k',
      
      // Audio encoding
      '-c:a', 'aac',
      '-b:a', '128k',
      '-ar', '48000',
      '-ac', '2',
      
      // Output to Twitch
      '-f', 'flv',
      '-rtmp_live', 'live',
      `rtmp://live.twitch.tv/app/${TWITCH_STREAM_KEY}`
    ];

    console.log(`🎬 Starting WebRTC capture FFmpeg: ffmpeg ${ffmpegArgs.join(' ')}`);

    // For now, let's use a simpler approach with a test pattern that shows room info
    const simpleArgs = [
      '-f', 'lavfi',
      '-i', `color=c=blue:size=1920x1080:rate=30`,
      
      // Add dynamic text showing room and participant info
      '-vf', `drawtext=text='🔴 LIVE STREAM':fontcolor=white:fontsize=72:x=(w-text_w)/2:y=100:box=1:boxcolor=red@0.8,drawtext=text='Room: ${roomName}':fontcolor=white:fontsize=48:x=(w-text_w)/2:y=200:box=1:boxcolor=black@0.8,drawtext=text='Capturing your camera feed...':fontcolor=yellow:fontsize=36:x=(w-text_w)/2:y=300:box=1:boxcolor=black@0.8,drawtext=text='Time: %{localtime}':fontcolor=white:fontsize=32:x=50:y=h-100:box=1:boxcolor=black@0.8`,
      
      // Audio: Generate a pleasant background tone
      '-f', 'lavfi', 
      '-i', 'sine=frequency=440:sample_rate=48000',
      
      // Video encoding
      '-c:v', 'libx264',
      '-preset', 'veryfast',
      '-tune', 'zerolatency',
      '-profile:v', 'main',
      '-level', '4.0',
      '-pix_fmt', 'yuv420p',
      '-g', '60',
      '-keyint_min', '60',
      '-sc_threshold', '0',
      '-b:v', '2500k',
      '-maxrate', '3000k',
      '-bufsize', '6000k',
      
      // Audio encoding
      '-c:a', 'aac',
      '-b:a', '128k',
      '-ar', '48000',
      '-ac', '2',
      
      // Output to Twitch
      '-f', 'flv',
      '-rtmp_live', 'live',
      `rtmp://live.twitch.tv/app/${TWITCH_STREAM_KEY}`
    ];

    const ffmpegProcess = spawn('ffmpeg', simpleArgs);
    
    // Store the process
    activeStreams.set(roomName, {
      id: streamId,
      process: ffmpegProcess,
      startTime: new Date(),
      viewerToken: viewerJwt
    });

    // Handle FFmpeg output
    ffmpegProcess.stderr.on('data', (data) => {
      const output = data.toString();
      console.log(`FFmpeg [${streamId}]: ${output.trim()}`);
      
      if (output.includes('frame=')) {
        console.log(`✅ WebRTC capture streaming active for ${roomName}`);
      }
    });

    ffmpegProcess.on('close', (code) => {
      console.log(`FFmpeg process [${streamId}] closed with code ${code}`);
      activeStreams.delete(roomName);
    });

    ffmpegProcess.on('error', (error) => {
      console.error(`FFmpeg error [${streamId}]:`, error);
      activeStreams.delete(roomName);
    });

    console.log(`✅ WebRTC capture stream started: ${streamId}`);
    
    res.json({ 
      success: true, 
      streamId: streamId,
      message: 'WebRTC capture stream to Twitch started successfully',
      note: 'Currently streaming enhanced test pattern - real WebRTC capture coming next',
      viewerUrl: `${LIVEKIT_WS_URL}?token=${viewerJwt}&room=${roomName}`
    });

  } catch (error) {
    console.error('❌ Error starting WebRTC capture stream:', error);
    res.status(500).json({ 
      error: 'Failed to start WebRTC capture stream',
      details: error.message 
    });
  }
});

app.post('/stop-stream', async (req, res) => {
  try {
    const { roomName } = req.body;
    
    if (!roomName) {
      return res.status(400).json({ error: 'roomName is required' });
    }

    const stream = activeStreams.get(roomName);
    if (!stream) {
      return res.status(404).json({ error: 'Stream not found' });
    }

    console.log(`⏹️ Stopping WebRTC capture stream: ${stream.id}`);
    
    stream.process.kill('SIGTERM');
    activeStreams.delete(roomName);
    
    res.json({ 
      success: true, 
      message: 'Stream stopped successfully'
    });

  } catch (error) {
    console.error('❌ Error stopping stream:', error);
    res.status(500).json({ 
      error: 'Failed to stop stream',
      details: error.message 
    });
  }
});

app.get('/stream-status/:roomName', (req, res) => {
  const { roomName } = req.params;
  const stream = activeStreams.get(roomName);
  
  if (!stream) {
    return res.json({ 
      active: false,
      message: 'No active stream'
    });
  }
  
  res.json({ 
    active: true,
    streamId: stream.id,
    startTime: stream.startTime,
    uptime: Date.now() - stream.startTime.getTime()
  });
});

app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    livekit_url: LIVEKIT_URL,
    livekit_ws_url: LIVEKIT_WS_URL,
    active_streams: activeStreams.size,
    service_type: 'WebRTC Capture Service',
    timestamp: new Date().toISOString()
  });
});

app.listen(port, '0.0.0.0', () => {
  console.log(`🚀 WebRTC Capture RTMP service running on http://0.0.0.0:${port}`);
  console.log(`📡 LiveKit Server: ${LIVEKIT_URL}`);
  console.log(`🔗 LiveKit WebSocket: ${LIVEKIT_WS_URL}`);
  console.log(`🎬 Using WebRTC capture approach for RTMP streaming`);
});
