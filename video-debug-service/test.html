<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Debug Test</title>
    <script src="/socket.io/socket.io.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        video { width: 320px; height: 240px; border: 1px solid #ccc; margin: 10px; }
        .controls { margin: 20px 0; }
        button { padding: 10px 20px; margin: 5px; font-size: 16px; }
        .log { background: #f0f0f0; padding: 10px; margin: 10px 0; height: 200px; overflow-y: scroll; font-family: monospace; font-size: 12px; }
        .stats { background: #e8f5e8; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Video Debug Test</h1>
        
        <div class="controls">
            <button onclick="startCamera()">📹 Start Camera</button>
            <button onclick="startRecording()">🔴 Start Recording</button>
            <button onclick="stopRecording()">⏹️ Stop Recording</button>
            <button onclick="clearLog()">🗑️ Clear Log</button>
        </div>

        <div>
            <video id="video" autoplay muted playsinline></video>
            <canvas id="canvas" style="display: none;"></canvas>
        </div>

        <div class="stats" id="stats">
            <strong>Status:</strong> Ready
        </div>

        <div class="log" id="log"></div>
    </div>

    <script>
        const socket = io('http://localhost:3002');
        let video = document.getElementById('video');
        let canvas = document.getElementById('canvas');
        let ctx = canvas.getContext('2d');
        let logDiv = document.getElementById('log');
        let statsDiv = document.getElementById('stats');
        
        let stream = null;
        let sessionId = null;
        let frameCount = 0;
        let captureInterval = null;
        let mediaRecorder = null;
        let recordedChunks = [];

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function updateStats(message) {
            statsDiv.innerHTML = `<strong>Status:</strong> ${message}`;
        }

        function clearLog() {
            logDiv.innerHTML = '';
        }

        async function startCamera() {
            try {
                log('🎥 Requesting camera access...');
                updateStats('Starting camera...');
                
                stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 1920 },
                        height: { ideal: 1080 },
                        frameRate: { ideal: 30 }
                    },
                    audio: true
                });

                video.srcObject = stream;
                
                const videoTrack = stream.getVideoTracks()[0];
                const settings = videoTrack.getSettings();
                
                log(`✅ Camera started: ${settings.width}x${settings.height} @ ${settings.frameRate}fps`);
                updateStats(`Camera active: ${settings.width}x${settings.height}`);
                
                // Set canvas size to match video
                canvas.width = settings.width || 1920;
                canvas.height = settings.height || 1080;
                
            } catch (error) {
                log(`❌ Camera error: ${error.message}`);
                updateStats('Camera failed');
            }
        }

        function startRecording() {
            if (!stream) {
                log('❌ No camera stream available');
                return;
            }

            log('🔴 Starting recording...');
            updateStats('Starting recording...');
            
            // Method 1: MediaRecorder API (most reliable)
            try {
                recordedChunks = [];
                mediaRecorder = new MediaRecorder(stream, {
                    mimeType: 'video/webm;codecs=vp8'
                });

                mediaRecorder.ondataavailable = (event) => {
                    if (event.data.size > 0) {
                        recordedChunks.push(event.data);
                        
                        // Send chunk to server
                        const reader = new FileReader();
                        reader.onload = () => {
                            const arrayBuffer = reader.result;
                            socket.emit('video-chunk', {
                                sessionId: sessionId,
                                chunk: Array.from(new Uint8Array(arrayBuffer)),
                                chunkNumber: recordedChunks.length
                            });
                        };
                        reader.readAsArrayBuffer(event.data);
                        
                        log(`📦 Recorded chunk ${recordedChunks.length}: ${(event.data.size / 1024).toFixed(1)} KB`);
                    }
                };

                mediaRecorder.onstop = () => {
                    log('🛑 MediaRecorder stopped');
                };

                // Start recording
                socket.emit('start-recording', {});
                
            } catch (error) {
                log(`❌ MediaRecorder error: ${error.message}`);
                updateStats('Recording failed');
            }
        }

        function stopRecording() {
            if (mediaRecorder && mediaRecorder.state === 'recording') {
                mediaRecorder.stop();
                log('⏹️ Stopping MediaRecorder...');
            }
            
            if (captureInterval) {
                clearInterval(captureInterval);
                captureInterval = null;
                log('⏹️ Stopping frame capture...');
            }
            
            if (sessionId) {
                socket.emit('stop-recording', { sessionId });
            }
            
            updateStats('Recording stopped');
        }

        // Socket events
        socket.on('connect', () => {
            log('✅ Connected to debug service');
            updateStats('Connected');
        });

        socket.on('recording-started', (data) => {
            if (data.success) {
                sessionId = data.sessionId;
                log(`✅ Recording started: ${data.filename}`);
                updateStats(`Recording: ${data.filename}`);
                
                // Start MediaRecorder
                if (mediaRecorder) {
                    mediaRecorder.start(1000); // 1 second chunks
                    log('🎬 MediaRecorder started with 1s chunks');
                }
                
            } else {
                log(`❌ Recording failed: ${data.error}`);
                updateStats('Recording failed');
            }
        });

        socket.on('recording-stopped', (data) => {
            if (data.success) {
                const stats = data.stats;
                log(`✅ Recording saved: ${data.filename}`);
                log(`📊 Stats: ${stats.duration.toFixed(1)}s, ${stats.frameCount} frames, ${stats.averageFps.toFixed(1)} fps`);
                updateStats(`Saved: ${data.filename}`);
            } else {
                log(`❌ Stop recording failed: ${data.error}`);
            }
            
            sessionId = null;
            frameCount = 0;
        });

        socket.on('disconnect', () => {
            log('❌ Disconnected from debug service');
            updateStats('Disconnected');
        });

        // Auto-start for quick testing
        log('🚀 Video Debug Test Ready');
        log('👆 Click "Start Camera" then "Start Recording" to test');
    </script>
</body>
</html>
