const express = require('express');
const { Server } = require('socket.io');
const http = require('http');
const cors = require('cors');
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

const app = express();
const server = http.createServer(app);
const io = new Server(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  },
  maxHttpBufferSize: 10e6 // 10MB for video frames
});

app.use(cors());
app.use(express.json());
app.use(express.static(__dirname)); // Serve static files

// Create recordings directory
const recordingsDir = path.join(__dirname, 'recordings');
if (!fs.existsSync(recordingsDir)) {
  fs.mkdirSync(recordingsDir);
}

// Store active sessions
const sessions = new Map();

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    sessions: sessions.size
  });
});

// List recordings
app.get('/recordings', (req, res) => {
  try {
    const files = fs.readdirSync(recordingsDir)
      .filter(file => file.endsWith('.webm') || file.endsWith('.mp4'))
      .map(file => ({
        name: file,
        size: fs.statSync(path.join(recordingsDir, file)).size,
        created: fs.statSync(path.join(recordingsDir, file)).birthtime
      }));
    
    res.json({ recordings: files });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

io.on('connection', (socket) => {
  console.log(`🔌 Client connected: ${socket.id}`);

  socket.on('start-recording', (data) => {
    try {
      const sessionId = uuidv4();
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `recording-${timestamp}-${sessionId.substring(0, 8)}.webm`;
      const filepath = path.join(recordingsDir, filename);
      
      const session = {
        id: sessionId,
        filename,
        filepath,
        startTime: Date.now(),
        frameCount: 0,
        totalBytes: 0,
        writeStream: fs.createWriteStream(filepath)
      };
      
      sessions.set(sessionId, session);
      
      console.log(`🎬 Started recording session ${sessionId}`);
      console.log(`📁 Recording to: ${filename}`);
      
      socket.emit('recording-started', {
        sessionId,
        filename,
        success: true
      });
      
    } catch (error) {
      console.error('❌ Error starting recording:', error);
      socket.emit('recording-started', {
        success: false,
        error: error.message
      });
    }
  });

  socket.on('video-frame', (data) => {
    try {
      const { sessionId, frameData, frameNumber, timestamp } = data;
      const session = sessions.get(sessionId);
      
      if (!session) {
        console.error(`❌ Session ${sessionId} not found`);
        return;
      }
      
      // Convert base64 to buffer if needed
      let buffer;
      if (typeof frameData === 'string') {
        // Remove data URL prefix if present
        const base64Data = frameData.replace(/^data:.*,/, '');
        buffer = Buffer.from(base64Data, 'base64');
      } else {
        buffer = Buffer.from(frameData);
      }
      
      // Write frame to file
      session.writeStream.write(buffer);
      session.frameCount++;
      session.totalBytes += buffer.length;
      
      // Log progress every 30 frames (roughly 1 second at 30fps)
      if (session.frameCount % 30 === 0) {
        const duration = (Date.now() - session.startTime) / 1000;
        const fps = session.frameCount / duration;
        const mbytes = (session.totalBytes / 1024 / 1024).toFixed(2);
        
        console.log(`📹 Session ${sessionId}: ${session.frameCount} frames, ${fps.toFixed(1)} fps, ${mbytes} MB`);
      }
      
    } catch (error) {
      console.error('❌ Error processing video frame:', error);
    }
  });

  socket.on('video-chunk', (data) => {
    try {
      const { sessionId, chunk, chunkNumber } = data;
      const session = sessions.get(sessionId);
      
      if (!session) {
        console.error(`❌ Session ${sessionId} not found`);
        return;
      }
      
      // Write chunk directly to file
      const buffer = Buffer.from(chunk);
      session.writeStream.write(buffer);
      session.totalBytes += buffer.length;
      
      if (chunkNumber % 10 === 0) {
        const duration = (Date.now() - session.startTime) / 1000;
        const mbytes = (session.totalBytes / 1024 / 1024).toFixed(2);
        
        console.log(`📦 Session ${sessionId}: chunk ${chunkNumber}, ${duration.toFixed(1)}s, ${mbytes} MB`);
      }
      
    } catch (error) {
      console.error('❌ Error processing video chunk:', error);
    }
  });

  socket.on('stop-recording', (data) => {
    try {
      const { sessionId } = data;
      const session = sessions.get(sessionId);
      
      if (!session) {
        console.error(`❌ Session ${sessionId} not found`);
        socket.emit('recording-stopped', {
          success: false,
          error: 'Session not found'
        });
        return;
      }
      
      // Close the write stream
      session.writeStream.end();
      
      const duration = (Date.now() - session.startTime) / 1000;
      const mbytes = (session.totalBytes / 1024 / 1024).toFixed(2);
      
      console.log(`🛑 Recording stopped for session ${sessionId}`);
      console.log(`📊 Final stats: ${session.frameCount} frames, ${duration.toFixed(1)}s, ${mbytes} MB`);
      console.log(`📁 Saved to: ${session.filename}`);
      
      sessions.delete(sessionId);
      
      socket.emit('recording-stopped', {
        sessionId,
        filename: session.filename,
        stats: {
          duration: duration,
          frameCount: session.frameCount,
          totalBytes: session.totalBytes,
          averageFps: session.frameCount / duration
        },
        success: true
      });
      
    } catch (error) {
      console.error('❌ Error stopping recording:', error);
      socket.emit('recording-stopped', {
        success: false,
        error: error.message
      });
    }
  });

  socket.on('disconnect', () => {
    console.log(`🔌 Client disconnected: ${socket.id}`);
    
    // Clean up any sessions for this socket
    for (const [sessionId, session] of sessions) {
      if (session.socketId === socket.id) {
        session.writeStream.end();
        sessions.delete(sessionId);
        console.log(`🧹 Cleaned up session ${sessionId} for disconnected client`);
      }
    }
  });
});

const PORT = process.env.PORT || 3002;
server.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Video Debug Service running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`📁 Recordings API: http://localhost:${PORT}/recordings`);
  console.log(`📂 Recordings saved to: ${recordingsDir}`);
});
